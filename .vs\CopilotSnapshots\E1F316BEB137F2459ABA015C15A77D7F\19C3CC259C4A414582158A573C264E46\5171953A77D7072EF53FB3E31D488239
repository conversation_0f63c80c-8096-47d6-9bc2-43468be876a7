﻿{
  "Provider": "SqlServer",
  "UseDocker": true,
  "ConnectionStrings": {
    "SqlServer": "Server=(localdb)\\mssqllocaldb;Database=Q_FilterBuilder_E2E_Tests;Trusted_Connection=true;",
    "MySql": "Server=localhost;Database=Q_FilterBuilder_E2E_Tests;Uid=root;Pwd=password;",
    "PostgreSql": "Host=localhost;Database=Q_FilterBuilder_E2E_Tests;Username=postgres;Password=password;",
    "DockerSqlServer": "Server=localhost,1433;Database=Q_FilterBuilder_E2E_Tests;User Id=sa;Password=YourStrong@Passw0rd;",
    "DockerMySql": "Server=localhost;Database=testdb;Uid=root;Pwd=password;",
    "DockerPostgreSql": "Host=localhost;Database=testdb;Username=postgres;Password=password;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  }
}
