﻿# Q.FilterBuilder.IntegrationTests

Comprehensive integration test project for Q.FilterBuilder that validates the complete workflow from JSON input to actual database query execution using **Testcontainers** and **ASP.NET Core Test Server**.

## Overview

This test project validates the **complete real-world workflow** via web API endpoints:

1. **JSON Input** → **QueryBuilderConverter** → **FilterGroup**
2. **FilterGroup** → **IFilterBuilder** → **SQL Query + Parameters**
3. **Query Execution** → **Multiple ORMs** → **Database Results**
4. **Result Validation** → **Assert Expected Outcomes**

## Project Structure

```
Q.FilterBuilder.IntegrationTests/
├── Configuration/         # Provider configuration and settings
│   ├── DatabaseProvider.cs
│   └── TestConfiguration.cs
├── Controllers/           # ASP.NET Core test controllers
│   └── IntegrationTestController.cs
├── Database/             # Entity models and data seeding
│   ├── Models/
│   │   ├── User.cs
│   │   ├── Category.cs
│   │   └── Product.cs
│   ├── TestDbContext.cs
│   └── TestDataSeeder.cs
├── Infrastructure/       # Test base classes and startup
│   ├── IntegrationTestBase.cs
│   ├── IntegrationTestWebApplicationFactory.cs
│   └── TestStartup.cs
├── Tests/                # Integration test classes
│   ├── BasicFilterIntegrationTests.cs
│   ├── ComplexFilterIntegrationTests.cs
│   └── MultiProviderIntegrationTests.cs
├── JsonSamples/          # Sample JSON payloads
│   ├── basic-string-filter.json
│   ├── numeric-comparison-filter.json
│   ├── boolean-and-null-filter.json
│   ├── array-operations-filter.json
│   ├── datetime-filter.json
│   ├── complex-nested-filter.json
│   └── string-operations-filter.json
├── appsettings.test.json # Test configuration
├── run-tests.ps1         # PowerShell test runner
└── README.md
```

## Testcontainers Integration

The integration tests use **Testcontainers for .NET** to automatically manage database containers:

### Supported Databases
- **SQL Server** - `mcr.microsoft.com/mssql/server:2022-latest`
- **MySQL** - `mysql:8.0`
- **PostgreSQL** - `postgres:15`

### Automatic Container Management
- Containers are **automatically started** before each test class
- Containers are **automatically cleaned up** after each test class
- Each test class gets **fresh database instances**
- No manual Docker setup required

### Test Data
Each container is automatically populated with:
- **Users table** - 4 test users with various attributes
- **Categories table** - 3 product categories  
- **Products table** - 4 test products linked to categories

### Prerequisites
- Docker Desktop must be running
- No additional setup required - Testcontainers handles everything

## Test Classes

### BasicFilterIntegrationTests
**Basic integration tests covering fundamental operations:**
- String equality and contains operations
- Numeric comparisons (greater than, between)
- Boolean filtering
- Array operations (in, not_in)
- Null checks (is_null, is_not_null)
- DateTime filtering
- Query building validation

### ComplexFilterIntegrationTests
**Advanced integration tests for complex scenarios:**
- Nested group conditions (AND/OR combinations)
- Multiple OR conditions
- Between operator for ranges
- String operations (contains, begins_with, ends_with)
- Date range filtering
- Cross-table filtering (Products)

### MultiProviderIntegrationTests
**Cross-provider validation tests:**
- Consistent behavior across database providers
- Provider-specific syntax validation
- Type conversion testing
- Null handling verification
- Multiple ORM execution comparison

## Running Tests

### Using PowerShell Script (Recommended)

```powershell
# Run with default provider (SQL Server)
.\run-tests.ps1

# Run with specific provider
.\run-tests.ps1 -Provider MySql

# Run against multiple providers
.\run-tests.ps1 -ProvidersToTest "SqlServer,MySql,PostgreSql"

# Run in parallel
.\run-tests.ps1 -Parallel

# Run without Docker (requires local databases)
.\run-tests.ps1 -UseDocker:$false
```

### Using dotnet CLI

```bash
# Set environment variables
export DatabaseProvider=SqlServer
export UseDocker=true

# Run tests
dotnet test --configuration Debug
```

### Using Visual Studio
1. Ensure Docker Desktop is running
2. Set environment variables in Test Explorer settings
3. Run tests from Test Explorer

## Configuration

### Environment Variables
- `DatabaseProvider` - Database provider to use (SqlServer, MySql, PostgreSql)
- `UseDocker` - Whether to use Docker containers (true/false)
- `ProvidersToTest` - Comma-separated list of providers to test
- `RunInParallel` - Whether to run tests in parallel (true/false)

### appsettings.test.json
```json
{
  "DatabaseProvider": "SqlServer",
  "UseDocker": true,
  "ProvidersToTest": "SqlServer,MySql,PostgreSql",
  "RunInParallel": false,
  "ConnectionStrings": {
    "SqlServer": "Server=localhost,1433;Database=FilterBuilderTest;...",
    "MySql": "Server=localhost;Port=3306;Database=testdb;...",
    "PostgreSql": "Host=localhost;Port=5432;Database=testdb;..."
  }
}
```

## Test Data Schema

### Users Table
- **Id** (int, PK)
- **Name** (string, required)
- **Email** (string, required)
- **Age** (int)
- **Salary** (decimal)
- **IsActive** (bool)
- **CreatedDate** (datetime)
- **LastLoginDate** (datetime, nullable)
- **Department** (string, nullable)
- **Role** (string, nullable)
- **CategoryId** (int, FK, nullable)

### Categories Table
- **Id** (int, PK)
- **Name** (string, required)
- **Description** (string, nullable)
- **IsActive** (bool)
- **CreatedDate** (datetime)

### Products Table
- **Id** (int, PK)
- **Name** (string, required)
- **Description** (string, nullable)
- **Price** (decimal)
- **Stock** (int)
- **IsAvailable** (bool)
- **CreatedDate** (datetime)
- **UpdatedDate** (datetime, nullable)
- **Status** (string, nullable)
- **Rating** (double, nullable)
- **CategoryId** (int, FK)
- **CreatedByUserId** (int, FK, nullable)
- **Tags** (string, JSON array)
- **Metadata** (string, JSON object)

## ORM Testing

Each test validates query execution across multiple ORMs:

### Entity Framework Core
- Uses `FromSqlRaw` for parameterized queries
- Validates EF Core compatibility

### Dapper
- Direct SQL execution with dynamic parameters
- Validates micro-ORM compatibility

### ADO.NET
- Raw database connection and command execution
- Validates low-level database compatibility

## Troubleshooting

### Docker Issues
```bash
# Check Docker status
docker info

# Pull required images manually
docker pull mcr.microsoft.com/mssql/server:2022-latest
docker pull mysql:8.0
docker pull postgres:15
```

### Connection Issues
- Ensure Docker Desktop is running
- Check firewall settings
- Verify container ports are available

### Test Failures
- Check test output for specific error messages
- Verify test data matches expected values
- Ensure database schema is created correctly

## Contributing

When adding new tests:
1. Follow the existing naming conventions
2. Include comprehensive assertions
3. Test across all supported providers
4. Update JSON samples as needed
5. Document expected behavior clearly
