﻿using System.Data;
using Dapper;
using Microsoft.Data.SqlClient;
using MySqlConnector;
using Npgsql;
using Q.FilterBuilder.E2E.Tests.Configuration;

namespace Q.FilterBuilder.E2E.Tests.Database;

/// <summary>
/// Seeds test data for different database providers
/// </summary>
public class TestDataSeeder
{
    public static async Task SeedAsync(string connectionString, DatabaseProvider provider)
    {
        switch (provider)
        {
            case DatabaseProvider.SqlServer:
                await SeedSqlServerAsync(connectionString);
                break;
            case DatabaseProvider.MySql:
                await SeedMySqlAsync(connectionString);
                break;
            case DatabaseProvider.PostgreSql:
                await SeedPostgreSqlAsync(connectionString);
                break;
        }
    }

    private static async Task SeedSqlServerAsync(string connectionString)
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        // Create schema
        await connection.ExecuteAsync(GetSqlServerSchema());
        
        // Insert test data
        await connection.ExecuteAsync(GetSqlServerTestData());
    }

    private static async Task SeedMySqlAsync(string connectionString)
    {
        using var connection = new MySqlConnection(connectionString);
        await connection.OpenAsync();

        // Create schema
        await connection.ExecuteAsync(GetMySqlSchema());
        
        // Insert test data
        await connection.ExecuteAsync(GetMySqlTestData());
    }

    private static async Task SeedPostgreSqlAsync(string connectionString)
    {
        using var connection = new NpgsqlConnection(connectionString);
        await connection.OpenAsync();

        // Create schema
        await connection.ExecuteAsync(GetPostgreSqlSchema());
        
        // Insert test data
        await connection.ExecuteAsync(GetPostgreSqlTestData());
    }

    private static string GetSqlServerSchema()
    {
        return """
        CREATE TABLE Users (
            Id INT IDENTITY(1,1) PRIMARY KEY,
            Name NVARCHAR(100) NOT NULL,
            Email NVARCHAR(255) NOT NULL UNIQUE,
            Age INT NOT NULL,
            DateOfBirth DATETIME2 NOT NULL,
            IsActive BIT NOT NULL,
            IsVip BIT NOT NULL,
            City NVARCHAR(100),
            Country NVARCHAR(100),
            CreatedDate DATETIME2 NOT NULL,
            LastLoginDate DATETIME2,
            Salary DECIMAL(18,2)
        );

        CREATE TABLE Categories (
            Id INT IDENTITY(1,1) PRIMARY KEY,
            Name NVARCHAR(100) NOT NULL,
            Description NVARCHAR(500),
            IsActive BIT NOT NULL,
            CreatedDate DATETIME2 NOT NULL
        );

        CREATE TABLE Products (
            Id INT IDENTITY(1,1) PRIMARY KEY,
            Name NVARCHAR(200) NOT NULL,
            Description NVARCHAR(1000),
            Price DECIMAL(18,2) NOT NULL,
            Sku NVARCHAR(50),
            CategoryId INT NOT NULL,
            IsActive BIT NOT NULL,
            IsOnSale BIT NOT NULL,
            CreatedDate DATETIME2 NOT NULL,
            DiscontinuedDate DATETIME2,
            StockQuantity INT NOT NULL,
            FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
        );
        """;
    }

    private static string GetMySqlSchema()
    {
        return """
        CREATE TABLE Users (
            Id INT AUTO_INCREMENT PRIMARY KEY,
            Name VARCHAR(100) NOT NULL,
            Email VARCHAR(255) NOT NULL UNIQUE,
            Age INT NOT NULL,
            DateOfBirth DATETIME NOT NULL,
            IsActive BOOLEAN NOT NULL,
            IsVip BOOLEAN NOT NULL,
            City VARCHAR(100),
            Country VARCHAR(100),
            CreatedDate DATETIME NOT NULL,
            LastLoginDate DATETIME,
            Salary DECIMAL(18,2)
        );

        CREATE TABLE Categories (
            Id INT AUTO_INCREMENT PRIMARY KEY,
            Name VARCHAR(100) NOT NULL,
            Description VARCHAR(500),
            IsActive BOOLEAN NOT NULL,
            CreatedDate DATETIME NOT NULL
        );

        CREATE TABLE Products (
            Id INT AUTO_INCREMENT PRIMARY KEY,
            Name VARCHAR(200) NOT NULL,
            Description VARCHAR(1000),
            Price DECIMAL(18,2) NOT NULL,
            Sku VARCHAR(50),
            CategoryId INT NOT NULL,
            IsActive BOOLEAN NOT NULL,
            IsOnSale BOOLEAN NOT NULL,
            CreatedDate DATETIME NOT NULL,
            DiscontinuedDate DATETIME,
            StockQuantity INT NOT NULL,
            FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
        );
        """;
    }

    private static string GetPostgreSqlSchema()
    {
        return """
        CREATE TABLE Users (
            Id SERIAL PRIMARY KEY,
            Name VARCHAR(100) NOT NULL,
            Email VARCHAR(255) NOT NULL UNIQUE,
            Age INT NOT NULL,
            DateOfBirth TIMESTAMP NOT NULL,
            IsActive BOOLEAN NOT NULL,
            IsVip BOOLEAN NOT NULL,
            City VARCHAR(100),
            Country VARCHAR(100),
            CreatedDate TIMESTAMP NOT NULL,
            LastLoginDate TIMESTAMP,
            Salary DECIMAL(18,2)
        );

        CREATE TABLE Categories (
            Id SERIAL PRIMARY KEY,
            Name VARCHAR(100) NOT NULL,
            Description VARCHAR(500),
            IsActive BOOLEAN NOT NULL,
            CreatedDate TIMESTAMP NOT NULL
        );

        CREATE TABLE Products (
            Id SERIAL PRIMARY KEY,
            Name VARCHAR(200) NOT NULL,
            Description VARCHAR(1000),
            Price DECIMAL(18,2) NOT NULL,
            Sku VARCHAR(50),
            CategoryId INT NOT NULL,
            IsActive BOOLEAN NOT NULL,
            IsOnSale BOOLEAN NOT NULL,
            CreatedDate TIMESTAMP NOT NULL,
            DiscontinuedDate TIMESTAMP,
            StockQuantity INT NOT NULL,
            FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
        );
        """;
    }

    private static string GetSqlServerTestData()
    {
        return """
        INSERT INTO Categories (Name, Description, IsActive, CreatedDate) VALUES
        ('Electronics', 'Electronic devices and gadgets', 1, '2023-01-01'),
        ('Clothing', 'Apparel and accessories', 1, '2023-01-02'),
        ('Books', 'Books and publications', 0, '2023-01-03');

        INSERT INTO Users (Name, Email, Age, DateOfBirth, IsActive, IsVip, City, Country, CreatedDate, LastLoginDate, Salary) VALUES
        ('John Doe', '<EMAIL>', 35, '1988-05-15', 1, 0, 'New York', 'USA', '2023-01-01', '2023-12-01', 75000.00),
        ('Jane Smith', '<EMAIL>', 28, '1995-08-22', 1, 1, 'Los Angeles', 'USA', '2023-01-02', '2023-12-02', NULL),
        ('Bob Johnson', '<EMAIL>', 42, '1981-03-10', 0, 0, 'Chicago', 'USA', '2023-01-03', NULL, 85000.00),
        ('Alice Brown', '<EMAIL>', 31, '1992-11-05', 1, 1, 'Houston', 'USA', '2023-01-04', '2023-12-03', 95000.00);

        INSERT INTO Products (Name, Description, Price, Sku, CategoryId, IsActive, IsOnSale, CreatedDate, DiscontinuedDate, StockQuantity) VALUES
        ('Laptop Pro', 'High-performance laptop', 1299.99, 'LAP001', 1, 1, 0, '2023-01-01', NULL, 50),
        ('Smartphone X', 'Latest smartphone model', 899.99, 'PHN001', 1, 1, 1, '2023-01-02', NULL, 100),
        ('Cotton T-Shirt', 'Comfortable cotton t-shirt', 29.99, 'TSH001', 2, 1, 0, '2023-01-03', NULL, 200),
        ('Programming Guide', 'Complete programming guide', 49.99, 'BOK001', 3, 0, 0, '2023-01-04', '2023-06-01', 25);
        """;
    }

    private static string GetMySqlTestData()
    {
        return GetSqlServerTestData(); // Same data, MySQL syntax is compatible
    }

    private static string GetPostgreSqlTestData()
    {
        return """
        INSERT INTO Categories (Name, Description, IsActive, CreatedDate) VALUES
        ('Electronics', 'Electronic devices and gadgets', true, '2023-01-01'),
        ('Clothing', 'Apparel and accessories', true, '2023-01-02'),
        ('Books', 'Books and publications', false, '2023-01-03');

        INSERT INTO Users (Name, Email, Age, DateOfBirth, IsActive, IsVip, City, Country, CreatedDate, LastLoginDate, Salary) VALUES
        ('John Doe', '<EMAIL>', 35, '1988-05-15', true, false, 'New York', 'USA', '2023-01-01', '2023-12-01', 75000.00),
        ('Jane Smith', '<EMAIL>', 28, '1995-08-22', true, true, 'Los Angeles', 'USA', '2023-01-02', '2023-12-02', NULL),
        ('Bob Johnson', '<EMAIL>', 42, '1981-03-10', false, false, 'Chicago', 'USA', '2023-01-03', NULL, 85000.00),
        ('Alice Brown', '<EMAIL>', 31, '1992-11-05', true, true, 'Houston', 'USA', '2023-01-04', '2023-12-03', 95000.00);

        INSERT INTO Products (Name, Description, Price, Sku, CategoryId, IsActive, IsOnSale, CreatedDate, DiscontinuedDate, StockQuantity) VALUES
        ('Laptop Pro', 'High-performance laptop', 1299.99, 'LAP001', 1, true, false, '2023-01-01', NULL, 50),
        ('Smartphone X', 'Latest smartphone model', 899.99, 'PHN001', 1, true, true, '2023-01-02', NULL, 100),
        ('Cotton T-Shirt', 'Comfortable cotton t-shirt', 29.99, 'TSH001', 2, true, false, '2023-01-03', NULL, 200),
        ('Programming Guide', 'Complete programming guide', 49.99, 'BOK001', 3, false, false, '2023-01-04', '2023-06-01', 25);
        """;
    }
}
