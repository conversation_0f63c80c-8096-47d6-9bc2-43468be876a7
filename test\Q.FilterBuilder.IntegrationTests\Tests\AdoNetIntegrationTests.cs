using Q.FilterBuilder.IntegrationTests.Infrastructure;
using Xunit;

namespace Q.FilterBuilder.IntegrationTests.Tests;

/// <summary>
/// Integration tests for comprehensive complex scenario executed specifically with ADO.NET
/// Tests the same comprehensive complex scenario as EFCoreIntegrationTests but executed with ADO.NET
/// </summary>
public class AdoNetIntegrationTests : IntegrationTestBase
{
    private readonly JsonTestDataLoader _jsonLoader;

    public AdoNetIntegrationTests(IntegrationTestWebApplicationFactory factory, DatabaseContainerFixture containerFixture)
        : base(factory, containerFixture)
    {
        _jsonLoader = new JsonTestDataLoader();
    }

    [Fact]
    public async Task ComprehensiveScenario_AdoNet_ShouldExecuteSuccessfully()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("comprehensive-orm-test");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-comprehensive-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Same comprehensive scenario as EF Core and Dapper:
        // User.IsActive = true AND User.Age >= 25 AND (
        //   Product.Name contains "Premium" OR Product.Price > 100
        // ) AND (
        //   Category.Name in ["Electronics", "Software"] AND
        //   Category.IsActive = true AND
        //   Product.IsAvailable = true AND
        //   Product.Stock > 0
        // )
        Assert.NotEmpty(result); // Should return matching records from joined tables
    }

    [Fact]
    public async Task SimpleUserFilter_AdoNet_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("datatype-string-operations");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should execute string operations through ADO.NET
        Assert.Contains("John Doe", result);
        Assert.Contains("@company.com", result);
    }

    [Fact]
    public async Task ComplexNestedFilter_AdoNet_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("complex-nested-groups");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should handle complex nested groups through ADO.NET
        Assert.NotEmpty(result);
    }

    [Fact]
    public async Task JoinScenario_AdoNet_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("join-user-category");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should execute join queries through ADO.NET
        Assert.NotEmpty(result);
    }

    [Fact]
    public async Task NumericOperations_AdoNet_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("datatype-numeric-operations");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should handle numeric comparisons through ADO.NET
        Assert.Contains("John Doe", result);
        Assert.Contains("Alice Brown", result);
    }

    [Fact]
    public async Task DateTimeOperations_AdoNet_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("datatype-datetime-operations");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should handle DateTime operations through ADO.NET
        Assert.NotEmpty(result);
    }

    [Fact]
    public async Task ArrayOperations_AdoNet_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("datatype-array-operations");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should handle IN/NOT IN operations through ADO.NET
        Assert.NotEmpty(result);
    }

    [Fact]
    public async Task BooleanAndNullOperations_AdoNet_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("datatype-boolean-null-operations");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should handle boolean and null checks through ADO.NET
        Assert.Contains("John Doe", result);
        Assert.Contains("Jane Smith", result);
        Assert.Contains("Bob Johnson", result);
        Assert.Contains("Alice Brown", result);
    }

    [Fact]
    public async Task BuildQuery_AdoNet_ShouldReturnValidSqlQuery()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("comprehensive-orm-test");

        // Act
        var queryResult = await ExecuteFilterForQueryAsync(filterJson);

        // Assert
        Assert.NotNull(queryResult);
        Assert.NotEmpty(queryResult.Query);
        Assert.NotEmpty(queryResult.Parameters);
        
        // For ADO.NET, query should be raw SQL
        // Verify it contains expected SQL elements
        Assert.True(queryResult.Query.Contains("SELECT") || queryResult.Query.Contains("WHERE"));
        Assert.True(queryResult.Parameters.Length > 0);
        
        // Verify SQL contains table references
        Assert.True(queryResult.Query.Contains("User") || queryResult.Query.Contains("Product") || queryResult.Query.Contains("Category"));
    }

    [Fact]
    public async Task ParameterBinding_AdoNet_ShouldHandleCorrectly()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("complex-mixed-operators");

        // Act
        var queryResult = await ExecuteFilterForQueryAsync(filterJson);

        // Assert
        Assert.NotNull(queryResult);
        Assert.NotEmpty(queryResult.Query);
        Assert.NotEmpty(queryResult.Parameters);
        
        // Verify parameter placeholders in SQL (provider-specific)
        Assert.True(queryResult.Query.Contains("@") || queryResult.Query.Contains("?") || queryResult.Query.Contains("$"));
        
        // Verify parameter count matches expected operations
        Assert.True(queryResult.Parameters.Length >= 6);
    }

    [Fact]
    public async Task ConnectionManagement_AdoNet_ShouldHandleCorrectly()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("datatype-string-operations");

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should handle connection lifecycle properly
        Assert.NotEmpty(result);
    }

    [Fact]
    public async Task PerformanceTest_AdoNet_ShouldExecuteWithinReasonableTime()
    {
        // Arrange
        var filterJson = _jsonLoader.LoadTestData("complex-mixed-operators");
        var startTime = DateTime.UtcNow;

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-adonet-users", filterJson);

        // Assert
        var endTime = DateTime.UtcNow;
        var executionTime = endTime - startTime;
        
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should execute within reasonable time (adjust threshold as needed)
        Assert.True(executionTime.TotalSeconds < 30, $"ADO.NET execution took too long: {executionTime.TotalSeconds} seconds");
    }

    public override async Task DisposeAsync()
    {
        _jsonLoader?.Dispose();
        await base.DisposeAsync();
    }
}
