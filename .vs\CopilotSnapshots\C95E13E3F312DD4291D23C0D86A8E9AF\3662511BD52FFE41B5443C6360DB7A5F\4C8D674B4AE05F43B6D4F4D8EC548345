﻿using System.Text.Json;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Infrastructure;
using Xunit;

namespace Q.FilterBuilder.IntegrationTests.Tests;

/// <summary>
/// Tests specifically for LINQ provider functionality
/// </summary>
[Collection("DatabaseCollection")]
public class LinqProviderTests : IntegrationTestBase
{
    public LinqProviderTests(IntegrationTestWebApplicationFactory factory, DatabaseContainerFixture containerFixture)
        : base(factory, containerFixture)
    {
        // Only run these tests when LINQ provider is configured
        var provider = GetCurrentProvider();
        if (provider != DatabaseProvider.Linq)
        {
            // Skip these tests if not using LINQ provider
            return;
        }
    }

    [Fact]
    public async Task LinqProvider_BasicStringFilter_ShouldWorkWithInMemoryData()
    {
        // Skip if not LINQ provider
        if (GetCurrentProvider() != DatabaseProvider.Linq)
        {
            return;
        }

        // Arrange - Simple string filter
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Name",
                    "operator": "equal",
                    "value": "John Doe",
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();

        Assert.NotNull(content);
        Assert.Contains("John Doe", content);
    }

    [Fact]
    public async Task LinqProvider_ComplexQuery_ShouldHandleInMemoryOperations()
    {
        // Skip if not LINQ provider
        if (GetCurrentProvider() != DatabaseProvider.Linq)
        {
            return;
        }

        // Arrange - Complex query with multiple conditions
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Department",
                    "operator": "equal",
                    "value": "Technology",
                    "type": "string"
                },
                {
                    "field": "Age",
                    "operator": "greater",
                    "value": 25,
                    "type": "int"
                },
                {
                    "field": "IsActive",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        
        Assert.NotNull(content);
        // Should return users in Technology department, age > 25, and active
        Assert.Contains("Technology", content);
    }

    [Fact]
    public async Task LinqProvider_QueryBuilding_ShouldGenerateLinqExpressions()
    {
        // Skip if not LINQ provider
        if (GetCurrentProvider() != DatabaseProvider.Linq)
        {
            return;
        }

        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "OR",
            "rules": [
                {
                    "field": "Name",
                    "operator": "contains",
                    "value": "John",
                    "type": "string"
                },
                {
                    "field": "Age",
                    "operator": "between",
                    "value": [25, 35],
                    "type": "int"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/build-query", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        
        Assert.NotNull(content);
        // LINQ provider should generate expression trees instead of SQL
        Assert.Contains("query", content, StringComparison.OrdinalIgnoreCase);
        Assert.Contains("parameters", content, StringComparison.OrdinalIgnoreCase);
    }

    [Fact]
    public async Task LinqProvider_ArrayOperations_ShouldWorkWithInMemoryCollections()
    {
        // Skip if not LINQ provider
        if (GetCurrentProvider() != DatabaseProvider.Linq)
        {
            return;
        }

        // Arrange - Test IN operator with array
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Department",
                    "operator": "in",
                    "value": ["Technology", "Marketing"],
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        
        Assert.NotNull(content);
        // Should return users from Technology or Marketing departments
        Assert.True(content.Contains("Technology") || content.Contains("Marketing"));
    }

    [Fact]
    public async Task LinqProvider_NullHandling_ShouldWorkWithInMemoryData()
    {
        // Skip if not LINQ provider
        if (GetCurrentProvider() != DatabaseProvider.Linq)
        {
            return;
        }

        // Arrange - Test null operations
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "OR",
            "rules": [
                {
                    "field": "LastLoginDate",
                    "operator": "is_null",
                    "type": "datetime"
                },
                {
                    "field": "LastLoginDate",
                    "operator": "is_not_null",
                    "type": "datetime"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        
        Assert.NotNull(content);
        // Should return all users (some with null, some with non-null LastLoginDate)
    }

    private DatabaseProvider GetCurrentProvider()
    {
        var provider = Environment.GetEnvironmentVariable("DatabaseProvider") ?? "SqlServer";
        return Enum.Parse<DatabaseProvider>(provider, true);
    }
}
