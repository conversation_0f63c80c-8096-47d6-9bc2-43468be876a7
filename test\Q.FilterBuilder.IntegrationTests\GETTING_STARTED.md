# Getting Started with Q.FilterBuilder Integration Tests

## 🚀 Quick Start

### Prerequisites
1. **Docker Desktop** - Must be running for database containers
2. **.NET 8.0 SDK** - For building and running tests
3. **Git** - For cloning the repository

### Running Your First Test

```bash
# 1. Ensure Docker is running
docker info

# 2. Navigate to the integration tests directory
cd test/Q.FilterBuilder.IntegrationTests

# 3. Run the basic setup tests
dotnet test --filter "FullyQualifiedName~ProjectSetupTests"

# 4. Run a simple integration test
dotnet test --filter "FullyQualifiedName~BasicFilterIntegrationTests.BasicStringFilter_ShouldReturnCorrectResults"
```

## 🎯 What These Tests Do

The integration tests validate the **complete real-world workflow**:

1. **JSON Input** → A frontend query builder sends JSON like this:
   ```json
   {
     "condition": "AND",
     "rules": [
       {
         "field": "Name",
         "operator": "equal",
         "value": "John Doe",
         "type": "string"
       }
     ]
   }
   ```

2. **Query Building** → FilterBuilder converts this to SQL:
   ```sql
   -- SQL Server: [Name] = @p0
   -- MySQL: `Name` = ?p0  
   -- PostgreSQL: "Name" = $p0
   ```

3. **Database Execution** → The query runs against real databases using:
   - **Entity Framework Core**
   - **Dapper**
   - **ADO.NET**

4. **Result Validation** → Tests verify the results match expected data

## 🗄️ Test Data Overview

The tests use realistic data that you'll recognize:

### Users Table
- **John Doe** - Technology Developer, Age 30, Active
- **Jane Smith** - Marketing Manager, Age 28, Active  
- **Bob Johnson** - Finance Analyst, Age 35, Inactive
- **Alice Brown** - Technology Senior Developer, Age 32, Active

### Products Table
- **Laptop Pro** - $1,299.99, Technology category
- **Marketing Suite** - $299.99, Marketing category
- **Financial Dashboard** - $599.99, Finance category (Discontinued)
- **Code Editor Pro** - $99.99, Technology category

## 🧪 Test Categories

### 1. Basic Operations (`BasicFilterIntegrationTests`)
Test fundamental filtering operations:
```bash
dotnet test --filter "FullyQualifiedName~BasicFilterIntegrationTests"
```

**What it tests:**
- String equality: Find users named "John Doe"
- Numeric comparisons: Find users age >= 30
- Boolean filtering: Find active users
- Array operations: Find users in Technology or Marketing
- Null checks: Find users who have logged in
- Date filtering: Find users created after a date

### 2. Complex Scenarios (`ComplexFilterIntegrationTests`)
Test advanced filtering with nested conditions:
```bash
dotnet test --filter "FullyQualifiedName~ComplexFilterIntegrationTests"
```

**What it tests:**
- Nested AND/OR groups
- Between operations for ranges
- String operations (contains, starts with, ends with)
- Multiple table filtering

### 3. End-to-End Workflows (`EndToEndWorkflowTests`)
Test complete real-world scenarios:
```bash
dotnet test --filter "FullyQualifiedName~EndToEndWorkflowTests"
```

**What it tests:**
- Complete JSON-to-database workflows
- Type conversion across all data types
- Error handling
- Multi-ORM execution comparison

## 🔧 Configuration Options

### Environment Variables
Set these to customize test behavior:

```bash
# Windows (PowerShell)
$env:DatabaseProvider = "MySql"
$env:UseDocker = "true"

# Linux/Mac (Bash)
export DatabaseProvider=PostgreSql
export UseDocker=true
```

### Available Providers
- `SqlServer` (default)
- `MySql`
- `PostgreSql`

## 🐛 Troubleshooting

### Docker Issues
```bash
# Check if Docker is running
docker info

# Pull required images manually if needed
docker pull mcr.microsoft.com/mssql/server:2022-latest
docker pull mysql:8.0
docker pull postgres:15
```

### Build Issues
```bash
# Clean and rebuild
dotnet clean
dotnet build

# Restore packages
dotnet restore
```

### Test Failures
1. **Check Docker** - Ensure Docker Desktop is running
2. **Check Logs** - Look at test output for specific error messages
3. **Check Ports** - Ensure no other services are using database ports
4. **Check Memory** - Docker containers need sufficient memory

## 📊 Understanding Test Results

### Successful Test Output
```
✅ BasicStringFilter_ShouldReturnCorrectResults
✅ NumericComparisonFilter_ShouldReturnCorrectResults
✅ BooleanFilter_ShouldReturnCorrectResults
```

### What Success Means
- ✅ JSON was parsed correctly into FilterGroup
- ✅ SQL query was generated with correct syntax for the provider
- ✅ Database query executed successfully
- ✅ Results matched expected data
- ✅ All three ORMs (EF, Dapper, ADO.NET) returned consistent results

## 🎓 Learning from the Tests

### Example: Understanding a Basic Test

```csharp
[Fact]
public async Task BasicStringFilter_ShouldReturnCorrectResults()
{
    // 1. Create JSON (like from a frontend)
    var filterJson = JsonDocument.Parse("""
    {
        "condition": "AND",
        "rules": [
            {
                "field": "Name",
                "operator": "equal", 
                "value": "John Doe",
                "type": "string"
            }
        ]
    }
    """);

    // 2. Send to API endpoint
    var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

    // 3. Verify it worked
    response.EnsureSuccessStatusCode();
    var result = await response.Content.ReadAsStringAsync();
    Assert.Contains("John Doe", result);
}
```

**This test validates:**
1. JSON parsing works correctly
2. FilterBuilder generates valid SQL
3. Database query executes successfully
4. Results contain the expected user

## 🔄 Next Steps

1. **Run all tests** to see the full integration in action
2. **Examine the JSON samples** in `/JsonSamples/` for more examples
3. **Look at the controller** to understand the API endpoints
4. **Check the database models** to understand the test data structure
5. **Explore different providers** to see cross-database compatibility

## 💡 Tips for Success

- **Start with basic tests** before running complex scenarios
- **Use Docker Desktop** for the best experience
- **Check test output** for detailed error messages
- **Run tests sequentially** first, then try parallel execution
- **Use the health check endpoint** to verify system status

Happy testing! 🎉
