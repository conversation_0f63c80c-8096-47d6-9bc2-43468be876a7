﻿using System.Text.Json;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Infrastructure;
using Xunit;

namespace Q.FilterBuilder.IntegrationTests.Tests;

/// <summary>
/// Integration tests that validate consistent behavior across multiple database providers
/// </summary>
public class MultiProviderIntegrationTests : IntegrationTestBase
{
    public MultiProviderIntegrationTests(IntegrationTestWebApplicationFactory factory, DatabaseContainerFixture containerFixture)
        : base(factory, containerFixture)
    {
    }

    [Theory]
    [InlineData(DatabaseProvider.SqlServer)]
    [InlineData(DatabaseProvider.MySql)]
    [InlineData(DatabaseProvider.PostgreSql)]
    public async Task BasicStringFilter_ShouldWorkConsistentlyAcrossProviders(DatabaseProvider provider)
    {
        // This test would need to be run separately for each provider
        // For now, we'll test with the current provider
        if (Provider != provider)
        {
            return; // Skip if not the current provider
        }

        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Name",
                    "operator": "equal",
                    "value": "John Doe",
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        Assert.Contains("John Doe", result);
    }

    [Fact]
    public async Task QueryGeneration_ShouldProduceValidSyntaxForCurrentProvider()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Name",
                    "operator": "equal",
                    "value": "John Doe",
                    "type": "string"
                },
                {
                    "field": "Age",
                    "operator": "greater",
                    "value": 25,
                    "type": "int"
                }
            ]
        }
        """);

        // Act
        var queryResult = await ExecuteFilterForQueryAsync(filterJson);

        // Assert
        Assert.NotNull(queryResult);
        Assert.NotEmpty(queryResult.Query);
        Assert.Equal(2, queryResult.Parameters.Length);

        // Verify provider-specific syntax
        switch (Provider)
        {
            case DatabaseProvider.SqlServer:
                Assert.Contains("[Name]", queryResult.Query); // SQL Server uses square brackets
                Assert.Contains("@p", queryResult.Query);     // SQL Server uses @ parameters
                break;
            case DatabaseProvider.MySql:
                Assert.Contains("`Name`", queryResult.Query); // MySQL uses backticks
                Assert.Contains("?", queryResult.Query);      // MySQL uses ? parameters
                break;
            case DatabaseProvider.PostgreSql:
                Assert.Contains("\"Name\"", queryResult.Query); // PostgreSQL uses double quotes
                Assert.Contains("$", queryResult.Query);        // PostgreSQL uses $ parameters
                break;
        }
    }

    [Fact]
    public async Task ComplexQuery_ShouldExecuteSuccessfullyOnCurrentProvider()
    {
        // Arrange - Test a complex query with multiple operators
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "IsActive",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                },
                {
                    "field": "Department",
                    "operator": "in",
                    "value": ["Technology", "Marketing"],
                    "type": "string"
                }
            ],
            "groups": [
                {
                    "condition": "OR",
                    "rules": [
                        {
                            "field": "Age",
                            "operator": "greater",
                            "value": 30,
                            "type": "int"
                        },
                        {
                            "field": "Salary",
                            "operator": "greater",
                            "value": 70000,
                            "type": "decimal"
                        }
                    ]
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return John Doe (Tech, Age 30, Salary 75000, Active) and Alice Brown (Tech, Age 32, Salary 70000, Active)
        Assert.Contains("John Doe", result);
        Assert.Contains("Alice Brown", result);
    }

    [Fact]
    public async Task TypeConversion_ShouldWorkCorrectlyForCurrentProvider()
    {
        // Arrange - Test various data type conversions
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Age",
                    "operator": "equal",
                    "value": "30",
                    "type": "int"
                },
                {
                    "field": "Salary",
                    "operator": "equal",
                    "value": "75000.00",
                    "type": "decimal"
                },
                {
                    "field": "IsActive",
                    "operator": "equal",
                    "value": "true",
                    "type": "bool"
                },
                {
                    "field": "CreatedDate",
                    "operator": "equal",
                    "value": "2023-01-15T00:00:00Z",
                    "type": "datetime"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        Assert.Contains("John Doe", result); // Should match John Doe exactly
    }

    [Fact]
    public async Task NullHandling_ShouldWorkCorrectlyForCurrentProvider()
    {
        // Arrange - Test null value handling
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "OR",
            "rules": [
                {
                    "field": "LastLoginDate",
                    "operator": "is_null",
                    "type": "datetime"
                },
                {
                    "field": "LastLoginDate",
                    "operator": "is_not_null",
                    "type": "datetime"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return all users (some have null LastLoginDate, others don't)
        Assert.Contains("John Doe", result);
        Assert.Contains("Jane Smith", result);
        Assert.Contains("Bob Johnson", result);
        Assert.Contains("Alice Brown", result);
    }

    [Fact]
    public async Task HealthCheck_ShouldReturnCurrentProvider()
    {
        // Act
        var response = await Client.GetAsync("/api/IntegrationTest/health");

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        Assert.Contains("Healthy", result);
        Assert.Contains(Provider.ToString(), result);
    }

    [Fact]
    public async Task MultipleORMExecution_ShouldReturnConsistentResults()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "IsActive",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.NotNull(content);
        
        // The response should contain results from Entity Framework, Dapper, and ADO.NET
        Assert.Contains("entityFramework", content, StringComparison.OrdinalIgnoreCase);
        Assert.Contains("dapper", content, StringComparison.OrdinalIgnoreCase);
        Assert.Contains("adoNet", content, StringComparison.OrdinalIgnoreCase);

        // All three should return the same data (active users)
        var activeUserCount = content.Split("John Doe").Length - 1;
        Assert.True(activeUserCount >= 3); // Should appear at least 3 times (once per ORM)
    }
}
