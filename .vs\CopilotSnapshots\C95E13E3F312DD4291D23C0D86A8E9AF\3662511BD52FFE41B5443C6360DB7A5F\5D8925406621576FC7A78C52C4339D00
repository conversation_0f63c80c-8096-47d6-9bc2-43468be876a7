﻿{
  "DatabaseProvider": "PostgreSql",
  "UseDocker": true,
  "ProvidersToTest": "SqlServer,MySql,PostgreSql",
  "RunInParallel": false,
  "ConnectionStrings": {
    "SqlServer": "Server=localhost,1433;Database=FilterBuilderTest;User Id=sa;Password=YourStrong@Passw0rd123;TrustServerCertificate=true;",
    "MySql": "Server=localhost;Port=3306;Database=testdb;Uid=testuser;Pwd=testpass;",
    "PostgreSql": "Host=localhost;Port=5432;Database=testdb;Username=testuser;Password=testpass;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  }
}
