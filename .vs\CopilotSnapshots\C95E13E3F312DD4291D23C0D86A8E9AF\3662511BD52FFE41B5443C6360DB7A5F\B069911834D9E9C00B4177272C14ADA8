﻿using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;
using Testcontainers.MsSql;
using Testcontainers.MySql;
using Testcontainers.PostgreSql;
using Xunit;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure;

/// <summary>
/// Shared database container fixture that creates one container per provider for all tests
/// </summary>
public class DatabaseContainerFixture : IAsyncLifetime
{
    private MsSqlContainer? _sqlServerContainer;
    private MySqlContainer? _mysqlContainer;
    private PostgreSqlContainer? _postgresContainer;

    public DatabaseProvider Provider { get; private set; }
    public string ConnectionString { get; private set; } = string.Empty;

    public DatabaseContainerFixture()
    {
        // Get provider from configuration/environment
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["DatabaseProvider"] = "SqlServer", // Default provider
                ["UseDocker"] = "true"
            })
            .AddEnvironmentVariables()
            .Build();

        var testConfig = new TestConfiguration(configuration);
        Provider = testConfig.GetDatabaseProvider();
    }

    public async Task InitializeAsync()
    {
        // Start appropriate container based on provider
        switch (Provider)
        {
            case DatabaseProvider.SqlServer:
                _sqlServerContainer = new MsSqlBuilder()
                    .WithImage("mcr.microsoft.com/mssql/server:2022-latest")
                    .WithPassword("YourStrong@Passw0rd123")
                    .WithEnvironment("ACCEPT_EULA", "Y")
                    .WithEnvironment("MSSQL_PID", "Express")
                    .WithCleanUp(true)
                    .Build();

                await _sqlServerContainer.StartAsync();
                ConnectionString = _sqlServerContainer.GetConnectionString();
                break;

            case DatabaseProvider.MySql:
                _mysqlContainer = new MySqlBuilder()
                    .WithImage("mysql:8.0")
                    .WithDatabase("testdb")
                    .WithUsername("testuser")
                    .WithPassword("testpass")
                    .WithCleanUp(true)
                    .Build();

                await _mysqlContainer.StartAsync();
                ConnectionString = _mysqlContainer.GetConnectionString();
                break;

            case DatabaseProvider.PostgreSql:
                _postgresContainer = new PostgreSqlBuilder()
                    .WithImage("postgres:15")
                    .WithDatabase("testdb")
                    .WithUsername("testuser")
                    .WithPassword("testpass")
                    .WithCleanUp(true)
                    .Build();

                await _postgresContainer.StartAsync();
                ConnectionString = _postgresContainer.GetConnectionString();
                break;

            case DatabaseProvider.Linq:
                // LINQ provider uses in-memory database, no container needed
                ConnectionString = "InMemory";
                break;

            default:
                throw new NotSupportedException($"Provider {Provider} is not supported");
        }

        // Create and seed database once (skip for LINQ provider as it will be seeded per test)
        if (Provider != DatabaseProvider.Linq)
        {
            using var context = TestDataSeeder.CreateContext(Provider, ConnectionString);
            await TestDataSeeder.SeedAsync(context);
        }
    }

    public async Task DisposeAsync()
    {
        if (_sqlServerContainer != null)
            await _sqlServerContainer.DisposeAsync();
        if (_mysqlContainer != null)
            await _mysqlContainer.DisposeAsync();
        if (_postgresContainer != null)
            await _postgresContainer.DisposeAsync();
    }
}

/// <summary>
/// Test collection definition to share the database container across all test classes
/// </summary>
[CollectionDefinition("DatabaseCollection")]
public class DatabaseCollection : ICollectionFixture<DatabaseContainerFixture>
{
    // This class has no code, and is never created. Its purpose is simply
    // to be the place to apply [CollectionDefinition] and all the
    // ICollectionFixture<> interfaces.
}
