﻿using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Q.FilterBuilder.E2E.Tests.Configuration;
using Q.FilterBuilder.E2E.Tests.Database;
using Q.FilterBuilder.JsonConverter;
using Q.FilterBuilder.SqlServer.Extensions;
using Q.FilterBuilder.MySql.Extensions;
using Q.FilterBuilder.PostgreSql.Extensions;

namespace Q.FilterBuilder.E2E.Tests.Infrastructure;

/// <summary>
/// Test startup configuration for different database providers
/// </summary>
public class TestStartup
{
    private readonly IConfiguration _configuration;
    private readonly DatabaseProvider _provider;
    private readonly string _connectionString;

    public TestStartup(IConfiguration configuration, DatabaseProvider provider, string connectionString)
    {
        _configuration = configuration;
        _provider = provider;
        _connectionString = connectionString;
    }

    public void ConfigureServices(IServiceCollection services)
    {
        // Add controllers with JSON converter
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new QueryBuilderConverter());
            });

        // Add provider-specific FilterBuilder
        switch (_provider)
        {
            case DatabaseProvider.SqlServer:
                services.AddSqlServerFilterBuilder();
                services.AddDbContext<TestDbContext>(options =>
                    options.UseSqlServer(_connectionString));
                break;
            case DatabaseProvider.MySql:
                services.AddMySqlFilterBuilder();
                services.AddDbContext<TestDbContext>(options =>
                    options.UseMySql(_connectionString, Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(_connectionString)));
                break;
            case DatabaseProvider.PostgreSql:
                services.AddPostgreSqlFilterBuilder();
                services.AddDbContext<TestDbContext>(options =>
                    options.UseNpgsql(_connectionString));
                break;
        }

        // Add JSON converter
        services.AddSingleton<QueryBuilderConverter>();
        
        // Add React QueryBuilder converter
        services.AddSingleton(provider => new QueryBuilderConverter(new QueryBuilderOptions
        {
            ConditionPropertyName = "combinator"
        }));

        // Add test settings
        services.AddSingleton<TestSettingsProvider>();
        
        // Add logging
        services.AddLogging();
    }

    public void Configure(IApplicationBuilder app)
    {
        app.UseRouting();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });
    }
}

/// <summary>
/// Factory for creating test web applications with different providers
/// </summary>
public static class TestWebApplicationFactory
{
    public static WebApplication CreateApp(DatabaseProvider provider, string connectionString)
    {
        var builder = WebApplication.CreateBuilder();
        
        var startup = new TestStartup(builder.Configuration, provider, connectionString);
        startup.ConfigureServices(builder.Services);
        
        var app = builder.Build();
        startup.Configure(app);
        
        return app;
    }
}
