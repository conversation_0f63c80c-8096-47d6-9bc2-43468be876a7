﻿using Microsoft.EntityFrameworkCore;
using Q.FilterBuilder.IntegrationTests.Configuration;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Providers;

/// <summary>
/// Strategy interface for database provider-specific configuration and setup
/// </summary>
public interface IProviderStrategy
{
    /// <summary>
    /// The database provider this strategy handles
    /// </summary>
    DatabaseProvider Provider { get; }

    /// <summary>
    /// Configure FilterBuilder services for this provider
    /// </summary>
    /// <param name="services">Service collection to configure</param>
    void ConfigureFilterBuilder(IServiceCollection services);

    /// <summary>
    /// Configure Entity Framework DbContext for this provider
    /// </summary>
    /// <param name="options">DbContext options builder</param>
    /// <param name="connectionString">Database connection string</param>
    void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString);

    /// <summary>
    /// Get the connection string for this provider from configuration
    /// </summary>
    /// <param name="configuration">Application configuration</param>
    /// <returns>Connection string for this provider</returns>
    string GetConnectionString(IConfiguration configuration);

    /// <summary>
    /// Validate that the provider is properly configured and available
    /// </summary>
    /// <param name="connectionString">Connection string to validate</param>
    /// <returns>True if provider is available and configured correctly</returns>
    Task<bool> ValidateProviderAsync(string connectionString);

    /// <summary>
    /// Get provider-specific test configuration
    /// </summary>
    /// <returns>Provider-specific configuration options</returns>
    ProviderTestConfiguration GetTestConfiguration();
}

/// <summary>
/// Provider-specific test configuration
/// </summary>
public class ProviderTestConfiguration
{
    public string DisplayName { get; set; } = string.Empty;
    public bool RequiresContainer { get; set; } = true;
    public TimeSpan StartupTimeout { get; set; } = TimeSpan.FromMinutes(2);
    public Dictionary<string, object> AdditionalProperties { get; set; } = new();
}
