﻿using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Q.FilterBuilder.E2E.Tests.Configuration;
using Q.FilterBuilder.E2E.Tests.Controllers;
using Q.FilterBuilder.E2E.Tests.Database;
using Testcontainers.MsSql;
using Testcontainers.MySql;
using Testcontainers.PostgreSql;
using Xunit;

namespace Q.FilterBuilder.E2E.Tests.Infrastructure;

/// <summary>
/// Base class for E2E tests using Testcontainers and web test server
/// </summary>
public abstract class E2ETestBase : IAsyncLifetime
{
    protected DatabaseProvider Provider { get; private set; }
    protected string ConnectionString { get; private set; } = string.Empty;
    protected WebApplicationFactory<E2ETestController>? Factory { get; private set; }
    protected HttpClient? Client { get; private set; }
    
    private MsSqlContainer? _sqlServerContainer;
    private MySqlContainer? _mysqlContainer;
    private PostgreSqlContainer? _postgresContainer;

    protected E2ETestBase()
    {
        // Get provider from configuration/environment
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Provider"] = "MySql",
                ["UseDocker"] = "true"
            })
            .AddEnvironmentVariables()
            .Build();

        var settingsProvider = new TestSettingsProvider(configuration);
        Provider = settingsProvider.GetDatabaseProvider();
    }

    public virtual async Task InitializeAsync()
    {
        // Start appropriate container based on provider
        switch (Provider)
        {
            case DatabaseProvider.SqlServer:
                _sqlServerContainer = new MsSqlBuilder()
                .WithImage("mcr.microsoft.com/mssql/server:2019-latest")
                .WithPassword("YourStrong@Passw0rd123")
                .WithEnvironment("ACCEPT_EULA", "Y")
                .WithEnvironment("MSSQL_PID", "Express")
                .WithCleanUp(true)
                .Build();

                await _sqlServerContainer.StartAsync();
                ConnectionString = _sqlServerContainer.GetConnectionString();
                break;

            case DatabaseProvider.MySql:
                _mysqlContainer = new MySqlBuilder()
                    .WithImage("mysql:8.0")
                    .WithDatabase("testdb")
                    .WithUsername("root")
                    .WithPassword("password")
                    .WithCleanUp(true)
                    .Build();
                await _mysqlContainer.StartAsync();
                ConnectionString = _mysqlContainer.GetConnectionString();
                break;

            case DatabaseProvider.PostgreSql:
                _postgresContainer = new PostgreSqlBuilder()
                    .WithImage("postgres:15")
                    .WithDatabase("testdb")
                    .WithUsername("postgres")
                    .WithPassword("password")
                    .WithCleanUp(true)
                    .Build();
                await _postgresContainer.StartAsync();
                ConnectionString = _postgresContainer.GetConnectionString();
                break;
        }

        // Seed database with test data
        await TestDataSeeder.SeedAsync(ConnectionString, Provider);

        // Create web application factory
        Factory = new WebApplicationFactory<E2ETestController>()
            .WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    var startup = new TestStartup(new ConfigurationBuilder().Build(), Provider, ConnectionString);
                    startup.ConfigureServices(services);
                });

                builder.Configure(app =>
                {
                    var startup = new TestStartup(new ConfigurationBuilder().Build(), Provider, ConnectionString);
                    startup.Configure(app);
                });
            });

        Client = Factory.CreateClient();
    }

    public virtual async Task DisposeAsync()
    {
        Client?.Dispose();
        Factory?.Dispose();
        
        if (_sqlServerContainer != null)
            await _sqlServerContainer.DisposeAsync();
        if (_mysqlContainer != null)
            await _mysqlContainer.DisposeAsync();
        if (_postgresContainer != null)
            await _postgresContainer.DisposeAsync();
    }

    /// <summary>
    /// Execute complete E2E workflow via web API
    /// </summary>
    protected async Task<T?> ExecuteFilterAsync<T>(object filterJson)
    {
        var response = await Client!.PostAsJsonAsync("/api/E2ETest/execute-filter", filterJson);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<T>();
    }

    /// <summary>
    /// Execute custom query via web API
    /// </summary>
    protected async Task<T?> ExecuteCustomFilterAsync<T>(object filterJson, string baseQuery)
    {
        var request = new { FilterJson = filterJson, BaseQuery = baseQuery };
        var response = await Client!.PostAsJsonAsync("/api/E2ETest/execute-custom", request);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<T>();
    }

    /// <summary>
    /// Get health check information
    /// </summary>
    protected async Task<T?> GetHealthCheckAsync<T>()
    {
        var response = await Client!.GetAsync("/api/E2ETest/health");
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<T>();
    }

    /// <summary>
    /// Create JSON document from string
    /// </summary>
    protected static JsonDocument CreateJsonDocument(string json)
    {
        return JsonDocument.Parse(json);
    }

    /// <summary>
    /// Get current provider name for test output
    /// </summary>
    protected string GetProviderName() => Provider.ToString();
}
