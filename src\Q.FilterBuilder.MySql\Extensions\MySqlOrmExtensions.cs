using System;
using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;
using Dapper;

namespace Q.FilterBuilder.MySql.Extensions;

/// <summary>
/// MySQL specific parameter and query transformation extensions
/// </summary>
public static class MySqlOrmExtensions
{
    /// <summary>
    /// Convert MySQL query to EF Core raw query format
    /// </summary>
    /// <param name="whereClause"></param>
    /// <returns></returns>
    public static string ToEfRawQueryFormat(this string whereClause)
    {
        var efQuery = whereClause;
        // use regex to find all ? parameters
        var matches = Regex.Matches(efQuery, @"\?");

        // foreach matches and replace with {0}, {1}, etc.
        for (var i = 0; i < matches.Count; i++)
        {
            var index = efQuery.IndexOf("?");
            if (index >= 0)
            {
                efQuery = efQuery.Substring(0, index) + $"{{{i}}}" + efQuery.Substring(index + 1);
            }
        }

        return efQuery;
    }

    /// <summary>
    /// Convert MySQL query parameters for Dapper
    /// </summary>
    /// <param name="parameters"></param>
    /// <returns></returns>
    public static DynamicParameters ToDapperParameters(this object[] parameters)
    {
        var dynamicParams = new DynamicParameters();
        var provider = new MySqlFormatProvider();
        for (var i = 0; i < parameters.Length; i++)
        {
            dynamicParams.Add($"p{i}", parameters[i]);
        }
        return dynamicParams;
    }

    /// <summary>
    /// Create MySQL database parameters for ADO.NET
    /// </summary>
    /// <param name="command">The database command</param>
    /// <param name="parameters">Parameter values</param>
    public static void AddMySqlParameters(this IDbCommand command, object[] parameters)
    {
        var provider = new MySqlFormatProvider();
        for (var i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            // MySQL uses positional parameters, no need to set ParameterName
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }
    }

    /// <summary>
    /// Get MySQL table name with proper quoting
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <returns>Quoted table name</returns>
    public static string ToMySqlTableName(this string tableName)
    {
        var provider = new MySqlFormatProvider();
        return provider.FormatFieldName(tableName);
    }

    /// <summary>
    /// Get expected SQL query string for MySQL
    /// </summary>
    /// <param name="whereClause">WHERE clause</param>
    /// <param name="tableName">Table name</param>
    /// <returns>Complete SQL query</returns>
    public static string ToMySqlQuery(this string whereClause, string tableName = "Users")
    {
        return $"SELECT * FROM {tableName.ToMySqlTableName()} WHERE {whereClause}";
    }

    /// <summary>
    /// Validate MySQL parameter format
    /// </summary>
    /// <param name="sql">SQL query</param>
    /// <returns>True if valid MySQL parameter format</returns>
    public static bool IsValidMySqlParameterFormat(this string sql)
    {
        // MySQL uses ? for parameters
        return sql.Contains("?");
    }
}
