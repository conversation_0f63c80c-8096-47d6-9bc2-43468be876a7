﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="../Q.FilterBuilder.Core/Q.FilterBuilder.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.0" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <AssemblyName>Q.FilterBuilder.SqlServer</AssemblyName>
    <RootNamespace>Q.FilterBuilder.SqlServer</RootNamespace>
    <PackageId>Q.FilterBuilder.SqlServer</PackageId>
  </PropertyGroup>

</Project>
