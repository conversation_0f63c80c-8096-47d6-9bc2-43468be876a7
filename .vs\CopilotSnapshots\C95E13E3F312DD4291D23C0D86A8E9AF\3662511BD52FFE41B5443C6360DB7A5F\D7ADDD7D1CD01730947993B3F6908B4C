﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.SqlServer.Extensions;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Providers;

/// <summary>
/// SQL Server provider strategy implementation
/// </summary>
public class SqlServerProviderStrategy : IProviderStrategy
{
    public DatabaseProvider Provider => DatabaseProvider.SqlServer;

    public void ConfigureFilterBuilder(IServiceCollection services)
    {
        services.AddSqlServerFilterBuilder();
    }

    public void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseSqlServer(connectionString);
    }

    public string GetConnectionString(IConfiguration configuration)
    {
        return configuration.GetConnectionString("TestDatabase") ?? 
               throw new InvalidOperationException("SQL Server connection string not found");
    }

    public async Task<bool> ValidateProviderAsync(string connectionString)
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public ProviderTestConfiguration GetTestConfiguration()
    {
        return new ProviderTestConfiguration
        {
            DisplayName = "SQL Server",
            RequiresContainer = true,
            StartupTimeout = TimeSpan.FromMinutes(2),
            AdditionalProperties = new Dictionary<string, object>
            {
                ["DefaultPort"] = 1433,
                ["ImageName"] = "mcr.microsoft.com/mssql/server:2022-latest",
                ["AcceptEula"] = true
            }
        };
    }
}
