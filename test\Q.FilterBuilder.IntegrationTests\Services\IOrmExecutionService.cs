using Q.FilterBuilder.IntegrationTests.Database.Models;

namespace Q.FilterBuilder.IntegrationTests.Services;

/// <summary>
/// Interface for ORM-specific query execution
/// </summary>
public interface IOrmExecutionService
{
    /// <summary>
    /// Execute query using Entity Framework Core
    /// </summary>
    Task<List<User>> ExecuteWithEntityFrameworkAsync(string whereClause, object[] parameters);

    /// <summary>
    /// Execute query using Dapper
    /// </summary>
    Task<List<dynamic>> ExecuteWithDapperAsync(string whereClause, object[] parameters);

    /// <summary>
    /// Execute query using ADO.NET
    /// </summary>
    Task<List<dynamic>> ExecuteWithAdoNetAsync(string whereClause, object[] parameters);
}
