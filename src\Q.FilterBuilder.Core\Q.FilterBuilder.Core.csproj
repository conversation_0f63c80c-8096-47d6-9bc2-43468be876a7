﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.1;</TargetFrameworks>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <OutputType>Library</OutputType>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <AssemblyName>Q.FilterBuilder.Core</AssemblyName>
    <RootNamespace>Q.FilterBuilder.Core</RootNamespace>
    <PackageId>Q.FilterBuilder.Core</PackageId>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
  </ItemGroup>
</Project>
