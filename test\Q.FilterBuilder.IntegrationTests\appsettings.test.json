{"DatabaseProvider": "SqlServer", "SqlServer": {"ImageName": "mcr.microsoft.com/mssql/server:2022-latest", "Database": "FilterBuilderTest", "Username": "sa", "Password": "YourStrong@Passw0rd123", "Environment": {"ACCEPT_EULA": "Y", "MSSQL_PID": "Express"}}, "MySql": {"ImageName": "mysql:8.0", "Database": "testdb", "Username": "testuser", "Password": "testpass"}, "PostgreSql": {"ImageName": "postgres:15", "Database": "testdb", "Username": "testuser", "Password": "testpass"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}}