﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <AssemblyName>Q.FilterBuilder.JsonConverter</AssemblyName>
    <RootNamespace>Q.FilterBuilder.JsonConverter</RootNamespace>
    <PackageId>Q.FilterBuilder.JsonConverter</PackageId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../Q.FilterBuilder.Core/Q.FilterBuilder.Core.csproj" />
  </ItemGroup>
</Project>
