﻿using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.MySql.Extensions;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Providers;

/// <summary>
/// MySQL provider strategy implementation
/// </summary>
public class MySqlProviderStrategy : IProviderStrategy
{
    public DatabaseProvider Provider => DatabaseProvider.MySql;

    public void ConfigureFilterBuilder(IServiceCollection services)
    {
        services.AddMySqlFilterBuilder();
    }

    public void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
    }

    public string GetConnectionString(IConfiguration configuration)
    {
        return configuration.GetConnectionString("TestDatabase") ?? 
               throw new InvalidOperationException("MySQL connection string not found");
    }

    public async Task<bool> ValidateProviderAsync(string connectionString)
    {
        try
        {
            using var connection = new MySqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public ProviderTestConfiguration GetTestConfiguration()
    {
        return new ProviderTestConfiguration
        {
            DisplayName = "MySQL",
            RequiresContainer = true,
            StartupTimeout = TimeSpan.FromMinutes(2),
            AdditionalProperties = new Dictionary<string, object>
            {
                ["DefaultPort"] = 3306,
                ["ImageName"] = "mysql:8.0",
                ["RootPassword"] = "TestPassword123!"
            }
        };
    }
}
