using System;
using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;

namespace Q.FilterBuilder.PostgreSql.Extensions;

/// <summary>
/// PostgreSQL specific parameter and query transformation extensions
/// </summary>
public static class PostgreSqlOrmExtensions
{
    /// <summary>
    /// Convert PostgreSQL query to EF Core raw query format
    /// </summary>
    /// <param name="whereClause"></param>
    /// <returns></returns>
    public static string ToEfRawQueryFormat(this string whereClause)
    {
        var efQuery = whereClause;
        // use regex to find all $1, $2, etc.
        var matches = Regex.Matches(efQuery, @"\$(\d+)");

        // foreach matches and replace
        foreach (Match match in matches)
        {
            var index = int.Parse(match.Groups[1].Value);
            efQuery = efQuery.Replace(match.Value, $"{{{index - 1}}}"); // Convert to 0-based indexing
        }

        return efQuery;
    }

    /// <summary>
    /// Convert PostgreSQL query parameters for Dapper
    /// </summary>
    /// <param name="whereClause"></param>
    /// <param name="parameters"></param>
    /// <returns></returns>
    public static Dictionary<string, object> ToDapperParameters(this string whereClause, object[] parameters)
    {
        var paramDict = new Dictionary<string, object>();

        // Find all PostgreSQL parameters in the SQL and replace them
        var pgParamPattern = @"\$(\d+)";
        var matches = Regex.Matches(whereClause, pgParamPattern);

        // Create a mapping of PostgreSQL parameter indices to actual parameter array indices
        var paramMapping = new Dictionary<int, int>();
        for (var i = 0; i < matches.Count; i++)
        {
            var match = matches[i];
            var pgParamIndex = int.Parse(match.Groups[1].Value);
            if (!paramMapping.ContainsKey(pgParamIndex))
            {
                paramMapping[pgParamIndex] = i; // Map to sequential parameter array index
            }
        }

        // Create Dapper parameters
        foreach (var kvp in paramMapping)
        {
            var dapperParam = $"p{kvp.Value}";
            if (kvp.Value < parameters.Length)
            {
                paramDict[dapperParam] = parameters[kvp.Value];
            }
        }

        return paramDict;
    }

    /// <summary>
    /// Create PostgreSQL database parameters for ADO.NET
    /// </summary>
    /// <param name="command">The database command</param>
    /// <param name="parameters">Parameter values</param>
    public static void AddPostgreSqlParameters(this IDbCommand command, object[] parameters)
    {
        var provider = new PostgreSqlFormatProvider();
        for (var i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = provider.FormatParameterName(i);
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }
    }

    /// <summary>
    /// Get PostgreSQL table name with proper quoting
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <returns>Quoted table name</returns>
    public static string ToPostgreSqlTableName(this string tableName)
    {
        var provider = new PostgreSqlFormatProvider();
        return provider.FormatFieldName(tableName);
    }

    /// <summary>
    /// Get expected SQL query string for PostgreSQL
    /// </summary>
    /// <param name="whereClause">WHERE clause</param>
    /// <param name="tableName">Table name</param>
    /// <returns>Complete SQL query</returns>
    public static string ToPostgreSqlQuery(this string whereClause, string tableName = "Users")
    {
        return $"SELECT * FROM {tableName.ToPostgreSqlTableName()} WHERE {whereClause}";
    }

    /// <summary>
    /// Validate PostgreSQL parameter format
    /// </summary>
    /// <param name="sql">SQL query</param>
    /// <returns>True if valid PostgreSQL parameter format</returns>
    public static bool IsValidPostgreSqlParameterFormat(this string sql)
    {
        // PostgreSQL uses $1, $2, etc.
        return Regex.IsMatch(sql, @"\$\d+");
    }
}
