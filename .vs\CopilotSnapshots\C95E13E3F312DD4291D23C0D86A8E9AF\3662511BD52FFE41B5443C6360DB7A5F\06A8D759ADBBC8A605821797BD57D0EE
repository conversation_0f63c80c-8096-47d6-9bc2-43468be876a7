﻿using System.Text.Json;
using Q.FilterBuilder.IntegrationTests.Infrastructure;
using Xunit;

namespace Q.FilterBuilder.IntegrationTests.Tests;

/// <summary>
/// Complex integration tests for FilterBuilder functionality including nested groups
/// </summary>
public class ComplexFilterIntegrationTests : IntegrationTestBase
{
    public ComplexFilterIntegrationTests(IntegrationTestWebApplicationFactory factory, DatabaseContainerFixture containerFixture)
        : base(factory, containerFixture)
    {
    }

    [Fact]
    public async Task ComplexNestedFilter_ShouldReturnCorrectResults()
    {
        // Arrange - Active users in Technology OR Marketing with Age > 25 AND Salary > 60000
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "IsActive",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                }
            ],
            "groups": [
                {
                    "condition": "OR",
                    "rules": [
                        {
                            "field": "Department",
                            "operator": "equal",
                            "value": "Technology",
                            "type": "string"
                        },
                        {
                            "field": "Department",
                            "operator": "equal",
                            "value": "Marketing",
                            "type": "string"
                        }
                    ]
                },
                {
                    "condition": "AND",
                    "rules": [
                        {
                            "field": "Age",
                            "operator": "greater",
                            "value": 25,
                            "type": "int"
                        },
                        {
                            "field": "Salary",
                            "operator": "greater",
                            "value": 60000,
                            "type": "decimal"
                        }
                    ]
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return John Doe (Tech, Age 30, Salary 75000, Active), Alice Brown (Tech, Age 32, Salary 70000, Active),
        // and Jane Smith (Marketing, Age 28, Salary 65000, Active)
        Assert.Contains("John Doe", result);
        Assert.Contains("Alice Brown", result);
        Assert.Contains("Jane Smith", result); // Marketing, Age 28 > 25, Salary 65000 > 60000, Active
        Assert.DoesNotContain("Bob Johnson", result); // Not active
    }

    [Fact]
    public async Task MultipleORConditions_ShouldReturnCorrectResults()
    {
        // Arrange - Users who are either Managers OR in Technology OR have Salary > 80000
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "OR",
            "rules": [
                {
                    "field": "Role",
                    "operator": "equal",
                    "value": "Manager",
                    "type": "string"
                },
                {
                    "field": "Department",
                    "operator": "equal",
                    "value": "Technology",
                    "type": "string"
                },
                {
                    "field": "Salary",
                    "operator": "greater",
                    "value": 80000,
                    "type": "decimal"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return:
        // - Jane Smith (Manager)
        // - John Doe (Technology)
        // - Alice Brown (Technology)
        // - Bob Johnson (Salary 85000 > 80000)
        Assert.Contains("Jane Smith", result);  // Manager
        Assert.Contains("John Doe", result);    // Technology
        Assert.Contains("Alice Brown", result); // Technology
        Assert.Contains("Bob Johnson", result); // Salary > 80000
    }

    [Fact]
    public async Task BetweenOperator_ShouldReturnCorrectResults()
    {
        // Arrange - Users with age between 28 and 32 (inclusive)
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Age",
                    "operator": "between",
                    "value": [28, 32],
                    "type": "int"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return Jane Smith (28), John Doe (30), Alice Brown (32)
        // Should not return Bob Johnson (35)
        Assert.Contains("Jane Smith", result);  // Age 28
        Assert.Contains("John Doe", result);    // Age 30
        Assert.Contains("Alice Brown", result); // Age 32
        Assert.DoesNotContain("Bob Johnson", result); // Age 35
    }

    [Fact]
    public async Task NotInOperator_ShouldReturnCorrectResults()
    {
        // Arrange - Users not in Finance or Marketing departments
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Department",
                    "operator": "not_in",
                    "value": ["Finance", "Marketing"],
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return only Technology department users
        Assert.Contains("John Doe", result);    // Technology
        Assert.Contains("Alice Brown", result); // Technology
        Assert.DoesNotContain("Jane Smith", result);  // Marketing
        Assert.DoesNotContain("Bob Johnson", result); // Finance
    }

    [Fact]
    public async Task StringOperations_ShouldReturnCorrectResults()
    {
        // Arrange - Users whose name contains "o" OR email starts with "alice"
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "OR",
            "rules": [
                {
                    "field": "Name",
                    "operator": "contains",
                    "value": "o",
                    "type": "string"
                },
                {
                    "field": "Email",
                    "operator": "begins_with",
                    "value": "alice",
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return John Doe (contains "o"), Bob Johnson (contains "o"), Alice Brown (email starts with "alice")
        Assert.Contains("John Doe", result);    // Name contains "o"
        Assert.Contains("Bob Johnson", result); // Name contains "o"
        Assert.Contains("Alice Brown", result); // Email starts with "alice"
        Assert.DoesNotContain("Jane Smith", result); // No "o" in name, email doesn't start with "alice"
    }

    [Fact]
    public async Task DateRangeFilter_ShouldReturnCorrectResults()
    {
        // Arrange - Users created in 2023 (between Jan 1 and Dec 31, 2023)
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "CreatedDate",
                    "operator": "between",
                    "value": ["2023-01-01T00:00:00Z", "2023-12-31T23:59:59Z"],
                    "type": "datetime"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return all users since they were all created in 2023
        Assert.Contains("John Doe", result);    // 2023-01-15
        Assert.Contains("Jane Smith", result);  // 2023-02-01
        Assert.Contains("Bob Johnson", result); // 2023-03-01
        Assert.Contains("Alice Brown", result); // 2023-04-01
    }

    [Fact]
    public async Task ProductsFilter_ShouldReturnCorrectResults()
    {
        // Arrange - Active products with price between 100 and 1000
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "IsAvailable",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                },
                {
                    "field": "Price",
                    "operator": "between",
                    "value": [100, 1000],
                    "type": "decimal"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-products-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        
        // Should return Marketing Suite (299.99) only
        // Should not return Code Editor Pro (99.99 < 100), Laptop Pro (1299.99 > 1000) or Financial Dashboard (not available)
        Assert.Contains("Marketing Suite", result);  // Available, Price 299.99
        Assert.DoesNotContain("Code Editor Pro", result);  // Available but Price 99.99 < 100
        Assert.DoesNotContain("Laptop Pro", result); // Available but Price 1299.99 > 1000
        Assert.DoesNotContain("Financial Dashboard", result); // Not available
    }
}
