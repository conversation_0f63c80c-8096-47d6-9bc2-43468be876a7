<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Q.FilterBuilder.Examples.AspNetCore.JsonConverter</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\JsonConverter\src\Q.FilterBuilder.JsonConverter.csproj" />
    <ProjectReference Include="..\..\src\Core\src\Q.FilterBuilder.Core.csproj" />
  </ItemGroup>

</Project>
