﻿using Q.FilterBuilder.E2E.Tests.Configuration;
using Q.FilterBuilder.E2E.Tests.Database;
using Q.FilterBuilder.E2E.Tests.Infrastructure;
using Q.FilterBuilder.JsonConverter;
using Q.FilterBuilder.JsonConverter.Extensions;
using Q.FilterBuilder.SqlServer.Extensions;
using Q.FilterBuilder.MySql.Extensions;
using Q.FilterBuilder.PostgreSql.Extensions;
using Microsoft.EntityFrameworkCore;
using Testcontainers.MsSql;
using Testcontainers.MySql;
using Testcontainers.PostgreSql;

var builder = WebApplication.CreateBuilder(args);

// Get provider from environment or default to SqlServer
var configuration = builder.Configuration;
var settingsProvider = new TestSettingsProvider(configuration);
var provider = settingsProvider.GetDatabaseProvider();
var useDocker = settingsProvider.UseDocker();

Console.WriteLine($"🚀 Starting E2E Test Server");
Console.WriteLine($"📊 Provider: {provider}");
Console.WriteLine($"🐳 Use Docker: {useDocker}");

string connectionString = "";

if (useDocker)
{
    Console.WriteLine($"🐳 Starting {provider} container...");
    
    switch (provider)
    {
        case DatabaseProvider.SqlServer:
            Console.WriteLine($"⏳ Starting SQL Server container (this may take 2-3 minutes)...");
            var sqlContainer = new MsSqlBuilder()
                .WithImage("mcr.microsoft.com/mssql/server:2019-latest")
                .WithPassword("YourStrong@Passw0rd123")
                .WithEnvironment("ACCEPT_EULA", "Y")
                .WithEnvironment("MSSQL_PID", "Express")
                .WithCleanUp(true)
                .Build();

            await sqlContainer.StartAsync();
            connectionString = sqlContainer.GetConnectionString();
            Console.WriteLine($"✅ SQL Server container started");
            break;
            
        case DatabaseProvider.MySql:
            var mysqlContainer = new MySqlBuilder()
                .WithImage("mysql:8.0")
                .WithDatabase("testdb")
                .WithUsername("root")
                .WithPassword("password")
                .WithCleanUp(true)
                .Build();
            
            await mysqlContainer.StartAsync();
            connectionString = mysqlContainer.GetConnectionString();
            Console.WriteLine($"✅ MySQL container started");
            break;
            
        case DatabaseProvider.PostgreSql:
            var postgresContainer = new PostgreSqlBuilder()
                .WithImage("postgres:15")
                .WithDatabase("testdb")
                .WithUsername("postgres")
                .WithPassword("password")
                .WithCleanUp(true)
                .Build();
            
            await postgresContainer.StartAsync();
            connectionString = postgresContainer.GetConnectionString();
            Console.WriteLine($"✅ PostgreSQL container started");
            break;
    }
}
else
{
    connectionString = settingsProvider.GetConnectionString(provider);
    Console.WriteLine($"📝 Using connection string from configuration");
}

Console.WriteLine($"🔗 Connection String: {connectionString}");

// Add services to the container
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new QueryBuilderConverter());
    });

// Add provider-specific FilterBuilder
switch (provider)
{
    case DatabaseProvider.SqlServer:
        builder.Services.AddSqlServerFilterBuilder();
        builder.Services.AddDbContext<TestDbContext>(options =>
            options.UseSqlServer(connectionString));
        break;
    case DatabaseProvider.MySql:
        builder.Services.AddMySqlFilterBuilder();
        builder.Services.AddDbContext<TestDbContext>(options =>
            options.UseMySql(connectionString, Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(connectionString)));
        break;
    case DatabaseProvider.PostgreSql:
        builder.Services.AddPostgreSqlFilterBuilder();
        builder.Services.AddDbContext<TestDbContext>(options =>
            options.UseNpgsql(connectionString));
        break;
}

// Add JSON converter
builder.Services.AddSingleton<QueryBuilderConverter>();

// Add React QueryBuilder converter
builder.Services.AddSingleton(provider => new QueryBuilderConverter(new QueryBuilderOptions
{
    ConditionPropertyName = "combinator"
}));

// Add test settings
builder.Services.AddSingleton<TestSettingsProvider>();

// Add Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseRouting();
app.MapControllers();

// Seed database with test data
Console.WriteLine($"🌱 Seeding database...");
try
{
    await TestDataSeeder.SeedAsync(connectionString, provider);
    Console.WriteLine($"✅ Database seeded successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Failed to seed database: {ex.Message}");
    Console.WriteLine($"🔍 Full error: {ex}");
}

Console.WriteLine($"🌐 Server starting...");
Console.WriteLine($"📍 Swagger UI: https://localhost:5001/swagger");
Console.WriteLine($"🔗 Health Check: https://localhost:5001/api/E2ETest/health");

app.Run();
