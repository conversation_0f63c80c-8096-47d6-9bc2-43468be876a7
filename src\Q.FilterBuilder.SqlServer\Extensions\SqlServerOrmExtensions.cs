using System;
using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;

namespace Q.FilterBuilder.SqlServer.Extensions;

/// <summary>
/// SQL Server specific parameter and query transformation extensions
/// </summary>
public static class SqlServerOrmExtensions
{
    /// <summary>
    /// Convert SQL Server query to EF Core raw query format
    /// </summary>
    /// <param name="whereClause"></param>
    /// <returns></returns>
    public static string ToEfRawQueryFormat(this string whereClause)
    {
        var efQuery = whereClause;
        // use regex to find all @p0, @p1, etc.
        var matches = Regex.Matches(efQuery, @"@p\d+");

        // foreach matches and replace
        foreach (Match match in matches)
        {
            var index = int.Parse(match.Value.Substring(2));
            efQuery = efQuery.Replace(match.Value, $"{{{index}}}");
        }

        return efQuery;
    }

    /// <summary>
    /// Convert SQL Server query parameters for Dapper
    /// </summary>
    /// <param name="parameters"></param>
    /// <returns></returns>
    public static Dictionary<string, object> ToDapperParameters(this object[] parameters)
    {
        var paramDict = new Dictionary<string, object>();
        var provider = new SqlServerFormatProvider();
        for (var i = 0; i < parameters.Length; i++)
        {
            paramDict[provider.FormatParameterName(i)] = parameters[i];
        }
        return paramDict;
    }

    /// <summary>
    /// Create SQL Server database parameters for ADO.NET
    /// </summary>
    /// <param name="command">The database command</param>
    /// <param name="parameters">Parameter values</param>
    public static void AddSqlServerParameters(this IDbCommand command, object[] parameters)
    {
        var provider = new SqlServerFormatProvider();
        for (var i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = provider.FormatParameterName(i);
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }
    }

    /// <summary>
    /// Get SQL Server table name with proper quoting
    /// </summary>
    /// <param name="tableName">Table name</param>
    /// <returns>Quoted table name</returns>
    public static string ToSqlServerTableName(this string tableName)
    {
        var provider = new SqlServerFormatProvider();
        return provider.FormatFieldName(tableName);
    }

    /// <summary>
    /// Get expected SQL query string for SQL Server
    /// </summary>
    /// <param name="whereClause">WHERE clause</param>
    /// <param name="tableName">Table name</param>
    /// <returns>Complete SQL query</returns>
    public static string ToSqlServerQuery(this string whereClause, string tableName = "Users")
    {
        return $"SELECT * FROM {tableName.ToSqlServerTableName()} WHERE {whereClause}";
    }

    /// <summary>
    /// Validate SQL Server parameter format
    /// </summary>
    /// <param name="sql">SQL query</param>
    /// <returns>True if valid SQL Server parameter format</returns>
    public static bool IsValidSqlServerParameterFormat(this string sql)
    {
        // SQL Server uses @p0, @p1, etc.
        return sql.Contains("@p");
    }
}
