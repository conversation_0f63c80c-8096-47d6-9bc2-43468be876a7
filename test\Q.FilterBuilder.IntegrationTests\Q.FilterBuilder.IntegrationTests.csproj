<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <GenerateProgramFile>false</GenerateProgramFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    
    <!-- ASP.NET Core Test Server -->
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    
    <!-- Testcontainers for database management -->
    <PackageReference Include="Testcontainers" Version="4.4.0" />
    <PackageReference Include="Testcontainers.MsSql" Version="4.4.0" />
    <PackageReference Include="Testcontainers.MySql" Version="4.4.0" />
    <PackageReference Include="Testcontainers.PostgreSql" Version="4.4.0" />
    
    <!-- Entity Framework for schema creation and testing -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    
    <!-- Multiple ORM support for testing -->
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.0" />
    <PackageReference Include="MySqlConnector" Version="2.3.7" />
    <PackageReference Include="Npgsql" Version="8.0.5" />
    
    <!-- Configuration and DI -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <!-- FilterBuilder project references -->
    <ProjectReference Include="..\..\src\Q.FilterBuilder.Core\Q.FilterBuilder.Core.csproj" />
    <ProjectReference Include="..\..\src\Q.FilterBuilder.SqlServer\Q.FilterBuilder.SqlServer.csproj" />
    <ProjectReference Include="..\..\src\Q.FilterBuilder.MySql\Q.FilterBuilder.MySql.csproj" />
    <ProjectReference Include="..\..\src\Q.FilterBuilder.PostgreSql\Q.FilterBuilder.PostgreSql.csproj" />
    <ProjectReference Include="..\..\src\Q.FilterBuilder.Linq\Q.FilterBuilder.Linq.csproj" />
    <ProjectReference Include="..\..\src\Q.FilterBuilder.JsonConverter\Q.FilterBuilder.JsonConverter.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Include JSON test files -->
    <None Include="JsonSamples\**\*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="appsettings.test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
