﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <AssemblyName>Q.FilterBuilder.Linq</AssemblyName>
    <RootNamespace>Q.FilterBuilder.Linq</RootNamespace>
    <PackageId>Q.FilterBuilder.Linq</PackageId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../Q.FilterBuilder.Core/Q.FilterBuilder.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
  </ItemGroup>

</Project>
