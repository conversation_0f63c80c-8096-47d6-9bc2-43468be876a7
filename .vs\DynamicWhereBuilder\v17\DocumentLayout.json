{"Version": 1, "WorkspaceRootPath": "C:\\git-repos\\DynamicWhereBuilder\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.jsonconverter\\jquerybuilderconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|solutionrelative:src\\dynamicwhere.jsonconverter\\jquerybuilderconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.jsonconverter\\valueparsers\\jsonelementparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|solutionrelative:src\\dynamicwhere.jsonconverter\\valueparsers\\jsonelementparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.jsonconverter\\valueparsers\\primitivevalueparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|solutionrelative:src\\dynamicwhere.jsonconverter\\valueparsers\\primitivevalueparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.sqlserverprovider\\operators\\notinoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|solutionrelative:src\\dynamicwhere.sqlserverprovider\\operators\\notinoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.linqprovider\\operators\\notinoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|solutionrelative:src\\dynamicwhere.linqprovider\\operators\\notinoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.linqprovider\\operators\\notcontainsoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|solutionrelative:src\\dynamicwhere.linqprovider\\operators\\notcontainsoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.linqprovider\\operators\\containsoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|solutionrelative:src\\dynamicwhere.linqprovider\\operators\\containsoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.sqlserverprovider\\operators\\containsoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|solutionrelative:src\\dynamicwhere.sqlserverprovider\\operators\\containsoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.sqlserverprovider\\operators\\betweenoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|solutionrelative:src\\dynamicwhere.sqlserverprovider\\operators\\betweenoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.sqlserverprovider\\operators\\notcontainsoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|solutionrelative:src\\dynamicwhere.sqlserverprovider\\operators\\notcontainsoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\notcontainsoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\notcontainsoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\isnotemptyoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\isnotemptyoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\endswithoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\endswithoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\isemptyoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\isemptyoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\isnotnulloperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\isnotnulloperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\isnulloperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\isnulloperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\notbeginswithoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\notbeginswithoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\notbetweenoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\notbetweenoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\notendswithoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\notendswithoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\notinoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\notinoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\datediffoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\datediffoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\containsoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\containsoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\betweenoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\betweenoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\sqlserverprovider.tests\\operators\\beginswithoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{999301DC-0E6F-44EA-A06A-A79B3F72BCE5}|test\\SqlServerProvider.Tests\\SqlServerProvider.Tests.csproj|solutionrelative:test\\sqlserverprovider.tests\\operators\\beginswithoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3EF5C3A3-2B95-40C0-8E66-C48416A581F6}|test\\LinqProvider.Tests\\LinqProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\linqprovider.tests\\operators\\notbetweenoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3EF5C3A3-2B95-40C0-8E66-C48416A581F6}|test\\LinqProvider.Tests\\LinqProvider.Tests.csproj|solutionrelative:test\\linqprovider.tests\\operators\\notbetweenoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3EF5C3A3-2B95-40C0-8E66-C48416A581F6}|test\\LinqProvider.Tests\\LinqProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\linqprovider.tests\\operators\\inoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3EF5C3A3-2B95-40C0-8E66-C48416A581F6}|test\\LinqProvider.Tests\\LinqProvider.Tests.csproj|solutionrelative:test\\linqprovider.tests\\operators\\inoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\core.tests\\baseoperatorprovidertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|solutionrelative:test\\core.tests\\baseoperatorprovidertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.linqprovider\\operators\\notbetweenoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|solutionrelative:src\\dynamicwhere.linqprovider\\operators\\notbetweenoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.sqlserverprovider\\operators\\beginswithoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|solutionrelative:src\\dynamicwhere.sqlserverprovider\\operators\\beginswithoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.linqprovider\\operators\\inoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|solutionrelative:src\\dynamicwhere.linqprovider\\operators\\inoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3EF5C3A3-2B95-40C0-8E66-C48416A581F6}|test\\LinqProvider.Tests\\LinqProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\linqprovider.tests\\operators\\betweenoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3EF5C3A3-2B95-40C0-8E66-C48416A581F6}|test\\LinqProvider.Tests\\LinqProvider.Tests.csproj|solutionrelative:test\\linqprovider.tests\\operators\\betweenoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.linqprovider\\operators\\isnotnulloperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|solutionrelative:src\\dynamicwhere.linqprovider\\operators\\isnotnulloperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\core.tests\\operators\\simpleoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|solutionrelative:test\\core.tests\\operators\\simpleoperatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.core\\operators\\basicoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|solutionrelative:src\\dynamicwhere.core\\operators\\basicoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.core\\operators\\baseoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|solutionrelative:src\\dynamicwhere.core\\operators\\baseoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.core\\operators\\ioperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|solutionrelative:src\\dynamicwhere.core\\operators\\ioperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.sqlserverprovider\\operators\\isemptyoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|solutionrelative:src\\dynamicwhere.sqlserverprovider\\operators\\isemptyoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.linqprovider\\linqprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|solutionrelative:src\\dynamicwhere.linqprovider\\linqprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.core\\providers\\baseoperatorprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|solutionrelative:src\\dynamicwhere.core\\providers\\baseoperatorprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.jsonconverter\\valueparsers\\datetimevalueparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|solutionrelative:src\\dynamicwhere.jsonconverter\\valueparsers\\datetimevalueparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.jsonconverter\\ivalueparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5F35E107-F9B0-483A-84A8-2C5AA0D0678C}|src\\DynamicWhere.JsonConverter\\DynamicWhere.JsonConverter.csproj|solutionrelative:src\\dynamicwhere.jsonconverter\\ivalueparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9071EE2B-6143-4819-A896-5C6FCC36B6B3}|test\\JsonConverter.Tests\\JsonConverter.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\jsonconverter.tests\\jquerybuilderconvertertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9071EE2B-6143-4819-A896-5C6FCC36B6B3}|test\\JsonConverter.Tests\\JsonConverter.Tests.csproj|solutionrelative:test\\jsonconverter.tests\\jquerybuilderconvertertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\core.tests\\typeconversionhelpertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|solutionrelative:test\\core.tests\\typeconversionhelpertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.core\\helpers\\typeconversionhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|solutionrelative:src\\dynamicwhere.core\\helpers\\typeconversionhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\core.tests\\datetimehelpertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|solutionrelative:test\\core.tests\\datetimehelpertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.core\\helpers\\datetimehelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|solutionrelative:src\\dynamicwhere.core\\helpers\\datetimehelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.core\\models\\dynamicrule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\DynamicWhere.Core\\DynamicWhere.Core.csproj|solutionrelative:src\\dynamicwhere.core\\models\\dynamicrule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3EF5C3A3-2B95-40C0-8E66-C48416A581F6}|test\\LinqProvider.Tests\\LinqProvider.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\linqprovider.tests\\linqprovidertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3EF5C3A3-2B95-40C0-8E66-C48416A581F6}|test\\LinqProvider.Tests\\LinqProvider.Tests.csproj|solutionrelative:test\\linqprovider.tests\\linqprovidertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\core.tests\\mockoperatorprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D54642BC-2A60-4F44-901B-D64562FEE015}|test\\Core.Tests\\Core.Tests.csproj|solutionrelative:test\\core.tests\\mockoperatorprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.sqlserverprovider\\sqlserverprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|solutionrelative:src\\dynamicwhere.sqlserverprovider\\sqlserverprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.linqprovider\\operators\\betweenoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E1B12E62-A2E0-461E-8616-36DE6A4E3BD8}|src\\DynamicWhere.LinqProvider\\DynamicWhere.LinqProvider.csproj|solutionrelative:src\\dynamicwhere.linqprovider\\operators\\betweenoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\dynamicwhere.sqlserverprovider\\operators\\notbetweenoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9116958B-CBFB-4F22-A1C8-F56AD8356140}|src\\DynamicWhere.SqlServerProvider\\DynamicWhere.SqlServerProvider.csproj|solutionrelative:src\\dynamicwhere.sqlserverprovider\\operators\\notbetweenoperator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Document", "DocumentIndex": 12, "Title": "EndsWithOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\EndsWithOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\EndsWithOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\EndsWithOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\EndsWithOperatorTests.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAQwA8AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:51:18.607Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "IsEmptyOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\IsEmptyOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\IsEmptyOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\IsEmptyOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\IsEmptyOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:51:11.295Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "IsNotNullOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\IsNotNullOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\IsNotNullOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\IsNotNullOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\IsNotNullOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwsAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:50:54.965Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "IsNullOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\IsNullOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\IsNullOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\IsNullOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\IsNullOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwsAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:50:43.447Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "NotBeginsWithOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotBeginsWithOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\NotBeginsWithOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotBeginsWithOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\NotBeginsWithOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwsAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:50:29.564Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "NotBetweenOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotBetweenOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\NotBetweenOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotBetweenOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\NotBetweenOperatorTests.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAAAA4AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:50:05.369Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "NotEndsWithOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotEndsWithOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\NotEndsWithOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotEndsWithOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\NotEndsWithOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:49:32.212Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "PrimitiveValueParser.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\ValueParsers\\PrimitiveValueParser.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.JsonConverter\\ValueParsers\\PrimitiveValueParser.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\ValueParsers\\PrimitiveValueParser.cs", "RelativeToolTip": "src\\DynamicWhere.JsonConverter\\ValueParsers\\PrimitiveValueParser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T05:30:21.886Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "JsonElementParser.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\ValueParsers\\JsonElementParser.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.JsonConverter\\ValueParsers\\JsonElementParser.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\ValueParsers\\JsonElementParser.cs", "RelativeToolTip": "src\\DynamicWhere.JsonConverter\\ValueParsers\\JsonElementParser.cs", "ViewState": "AgIAABMAAAAAAAAAAAAgwCEAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T05:30:15.194Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "JQueryBuilderConverter.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\JQueryBuilderConverter.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.JsonConverter\\JQueryBuilderConverter.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\JQueryBuilderConverter.cs", "RelativeToolTip": "src\\DynamicWhere.JsonConverter\\JQueryBuilderConverter.cs", "ViewState": "AgIAACUAAAAAAAAAAAAgwDEAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T15:18:40.067Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ContainsOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\ContainsOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.SqlServerProvider\\Operators\\ContainsOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\ContainsOperator.cs", "RelativeToolTip": "src\\DynamicWhere.SqlServerProvider\\Operators\\ContainsOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T11:02:07.649Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "NotInOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\NotInOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.SqlServerProvider\\Operators\\NotInOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\NotInOperator.cs", "RelativeToolTip": "src\\DynamicWhere.SqlServerProvider\\Operators\\NotInOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T11:07:45.484Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ContainsOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\ContainsOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.LinqProvider\\Operators\\ContainsOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\ContainsOperator.cs", "RelativeToolTip": "src\\DynamicWhere.LinqProvider\\Operators\\ContainsOperator.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T11:02:21.563Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "NotContainsOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\NotContainsOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.LinqProvider\\Operators\\NotContainsOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\NotContainsOperator.cs", "RelativeToolTip": "src\\DynamicWhere.LinqProvider\\Operators\\NotContainsOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T11:03:15.442Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "NotInOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\NotInOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.LinqProvider\\Operators\\NotInOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\NotInOperator.cs", "RelativeToolTip": "src\\DynamicWhere.LinqProvider\\Operators\\NotInOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T11:07:31.161Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "NotContainsOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotContainsOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\NotContainsOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotContainsOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\NotContainsOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAkwAsAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:48:45.194Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "IsNotEmptyOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\IsNotEmptyOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\IsNotEmptyOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\IsNotEmptyOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\IsNotEmptyOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T05:26:06.032Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "NotContainsOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\NotContainsOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.SqlServerProvider\\Operators\\NotContainsOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\NotContainsOperator.cs", "RelativeToolTip": "src\\DynamicWhere.SqlServerProvider\\Operators\\NotContainsOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwBMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T11:34:46.809Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "BetweenOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\BetweenOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.SqlServerProvider\\Operators\\BetweenOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\BetweenOperator.cs", "RelativeToolTip": "src\\DynamicWhere.SqlServerProvider\\Operators\\BetweenOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T09:13:02.861Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "NotInOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotInOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\NotInOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\NotInOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\NotInOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:49:14.795Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "DateDiffOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\DateDiffOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\DateDiffOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\DateDiffOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\DateDiffOperatorTests.cs", "ViewState": "AgIAACgAAAAAAAAAAAAswDcAAABbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:46:04.838Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "BetweenOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\BetweenOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\BetweenOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\BetweenOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\BetweenOperatorTests.cs", "ViewState": "AgIAACEAAAAAAAAAAAAowBEAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:44:31.371Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "BeginsWithOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\BeginsWithOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\BeginsWithOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\BeginsWithOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\BeginsWithOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:44:01.088Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "NotBetweenOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\LinqProvider.Tests\\Operators\\NotBetweenOperatorTests.cs", "RelativeDocumentMoniker": "test\\LinqProvider.Tests\\Operators\\NotBetweenOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\LinqProvider.Tests\\Operators\\NotBetweenOperatorTests.cs", "RelativeToolTip": "test\\LinqProvider.Tests\\Operators\\NotBetweenOperatorTests.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAgwC0AAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:43:04.386Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "NotBetweenOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\NotBetweenOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.LinqProvider\\Operators\\NotBetweenOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\NotBetweenOperator.cs", "RelativeToolTip": "src\\DynamicWhere.LinqProvider\\Operators\\NotBetweenOperator.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowCgAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:32:50.181Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "InOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\InOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.LinqProvider\\Operators\\InOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\InOperator.cs", "RelativeToolTip": "src\\DynamicWhere.LinqProvider\\Operators\\InOperator.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABIAAABTAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T09:55:49.74Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "IsNotNullOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\IsNotNullOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.LinqProvider\\Operators\\IsNotNullOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\IsNotNullOperator.cs", "RelativeToolTip": "src\\DynamicWhere.LinqProvider\\Operators\\IsNotNullOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T09:01:23.707Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "IsEmptyOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\IsEmptyOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.SqlServerProvider\\Operators\\IsEmptyOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\IsEmptyOperator.cs", "RelativeToolTip": "src\\DynamicWhere.SqlServerProvider\\Operators\\IsEmptyOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T09:18:04.092Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "BasicOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Operators\\BasicOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.Core\\Operators\\BasicOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Operators\\BasicOperator.cs", "RelativeToolTip": "src\\DynamicWhere.Core\\Operators\\BasicOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T08:33:35.145Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "IOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Operators\\IOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.Core\\Operators\\IOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Operators\\IOperator.cs", "RelativeToolTip": "src\\DynamicWhere.Core\\Operators\\IOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T08:33:20.83Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "BaseOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Operators\\BaseOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.Core\\Operators\\BaseOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Operators\\BaseOperator.cs", "RelativeToolTip": "src\\DynamicWhere.Core\\Operators\\BaseOperator.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAsAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T11:35:01.041Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "ContainsOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\ContainsOperatorTests.cs", "RelativeDocumentMoniker": "test\\SqlServerProvider.Tests\\Operators\\ContainsOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\SqlServerProvider.Tests\\Operators\\ContainsOperatorTests.cs", "RelativeToolTip": "test\\SqlServerProvider.Tests\\Operators\\ContainsOperatorTests.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAswBsAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T05:24:55.546Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "BaseOperatorProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Providers\\BaseOperatorProvider.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.Core\\Providers\\BaseOperatorProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Providers\\BaseOperatorProvider.cs", "RelativeToolTip": "src\\DynamicWhere.Core\\Providers\\BaseOperatorProvider.cs", "ViewState": "AgIAACcAAAAAAAAAAAAjwCQAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T03:33:57.506Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "BetweenOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\LinqProvider.Tests\\Operators\\BetweenOperatorTests.cs", "RelativeDocumentMoniker": "test\\LinqProvider.Tests\\Operators\\BetweenOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\LinqProvider.Tests\\Operators\\BetweenOperatorTests.cs", "RelativeToolTip": "test\\LinqProvider.Tests\\Operators\\BetweenOperatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAH4AAABXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T04:57:43.14Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "BaseOperatorProviderTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\BaseOperatorProviderTests.cs", "RelativeDocumentMoniker": "test\\Core.Tests\\BaseOperatorProviderTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\BaseOperatorProviderTests.cs", "RelativeToolTip": "test\\Core.Tests\\BaseOperatorProviderTests.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAswFMAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T16:20:09.424Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "InOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\LinqProvider.Tests\\Operators\\InOperatorTests.cs", "RelativeDocumentMoniker": "test\\LinqProvider.Tests\\Operators\\InOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\LinqProvider.Tests\\Operators\\InOperatorTests.cs", "RelativeToolTip": "test\\LinqProvider.Tests\\Operators\\InOperatorTests.cs", "ViewState": "AgIAABQAAAAAAAAAAADwvxgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T04:42:22.579Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "IValueParser.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\IValueParser.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.JsonConverter\\IValueParser.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\IValueParser.cs", "RelativeToolTip": "src\\DynamicWhere.JsonConverter\\IValueParser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T05:30:27.329Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "BeginsWithOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\BeginsWithOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.SqlServerProvider\\Operators\\BeginsWithOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\BeginsWithOperator.cs", "RelativeToolTip": "src\\DynamicWhere.SqlServerProvider\\Operators\\BeginsWithOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAQAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T11:34:18.385Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "SimpleOperatorTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\Operators\\SimpleOperatorTests.cs", "RelativeDocumentMoniker": "test\\Core.Tests\\Operators\\SimpleOperatorTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\Operators\\SimpleOperatorTests.cs", "RelativeToolTip": "test\\Core.Tests\\Operators\\SimpleOperatorTests.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAkwBUAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T16:20:25.513Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "TypeConversionHelperTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\TypeConversionHelperTests.cs", "RelativeDocumentMoniker": "test\\Core.Tests\\TypeConversionHelperTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\TypeConversionHelperTests.cs", "RelativeToolTip": "test\\Core.Tests\\TypeConversionHelperTests.cs", "ViewState": "AgIAAKEAAAAAAAAAAAAuwMQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T14:55:56.841Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "LinqProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\LinqProvider.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.LinqProvider\\LinqProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\LinqProvider.cs", "RelativeToolTip": "src\\DynamicWhere.LinqProvider\\LinqProvider.cs", "ViewState": "AgIAAAcAAAAAAAAAAAA1wBkAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T16:21:58.644Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "DateTimeValueParser.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\ValueParsers\\DateTimeValueParser.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.JsonConverter\\ValueParsers\\DateTimeValueParser.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.JsonConverter\\ValueParsers\\DateTimeValueParser.cs", "RelativeToolTip": "src\\DynamicWhere.JsonConverter\\ValueParsers\\DateTimeValueParser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwBMAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T11:34:07.009Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "TypeConversionHelper.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Helpers\\TypeConversionHelper.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.Core\\Helpers\\TypeConversionHelper.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Helpers\\TypeConversionHelper.cs", "RelativeToolTip": "src\\DynamicWhere.Core\\Helpers\\TypeConversionHelper.cs", "ViewState": "AgIAADcAAAAAAAAAAAAlwEIAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T14:55:35.286Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "DateTimeHelperTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\DateTimeHelperTests.cs", "RelativeDocumentMoniker": "test\\Core.Tests\\DateTimeHelperTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\DateTimeHelperTests.cs", "RelativeToolTip": "test\\Core.Tests\\DateTimeHelperTests.cs", "ViewState": "AgIAABYAAAAAAAAAAAAzwCYAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T05:06:47.158Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "DateTimeHelper.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Helpers\\DateTimeHelper.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.Core\\Helpers\\DateTimeHelper.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Helpers\\DateTimeHelper.cs", "RelativeToolTip": "src\\DynamicWhere.Core\\Helpers\\DateTimeHelper.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAABcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T03:50:42.973Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "JQueryBuilderConverterTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\JsonConverter.Tests\\JQueryBuilderConverterTests.cs", "RelativeDocumentMoniker": "test\\JsonConverter.Tests\\JQueryBuilderConverterTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\JsonConverter.Tests\\JQueryBuilderConverterTests.cs", "RelativeToolTip": "test\\JsonConverter.Tests\\JQueryBuilderConverterTests.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAIwDEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T14:54:18.017Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "DynamicRule.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Models\\DynamicRule.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.Core\\Models\\DynamicRule.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.Core\\Models\\DynamicRule.cs", "RelativeToolTip": "src\\DynamicWhere.Core\\Models\\DynamicRule.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAxwC0AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T03:28:25.148Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "MockOperatorProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\MockOperatorProvider.cs", "RelativeDocumentMoniker": "test\\Core.Tests\\MockOperatorProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Core.Tests\\MockOperatorProvider.cs", "RelativeToolTip": "test\\Core.Tests\\MockOperatorProvider.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T15:03:22.284Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "LinqProviderTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\LinqProvider.Tests\\LinqProviderTests.cs", "RelativeDocumentMoniker": "test\\LinqProvider.Tests\\LinqProviderTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\LinqProvider.Tests\\LinqProviderTests.cs", "RelativeToolTip": "test\\LinqProvider.Tests\\LinqProviderTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T16:20:56.865Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "SqlServerProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\SqlServerProvider.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.SqlServerProvider\\SqlServerProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\SqlServerProvider.cs", "RelativeToolTip": "src\\DynamicWhere.SqlServerProvider\\SqlServerProvider.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T16:23:43.265Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "BetweenOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\BetweenOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.LinqProvider\\Operators\\BetweenOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.LinqProvider\\Operators\\BetweenOperator.cs", "RelativeToolTip": "src\\DynamicWhere.LinqProvider\\Operators\\BetweenOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAcAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T11:34:59.243Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "NotBetweenOperator.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\NotBetweenOperator.cs", "RelativeDocumentMoniker": "src\\DynamicWhere.SqlServerProvider\\Operators\\NotBetweenOperator.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\DynamicWhere.SqlServerProvider\\Operators\\NotBetweenOperator.cs", "RelativeToolTip": "src\\DynamicWhere.SqlServerProvider\\Operators\\NotBetweenOperator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACUAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T11:34:30.709Z"}]}]}]}